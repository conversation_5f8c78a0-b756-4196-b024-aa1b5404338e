﻿
using GlobalTrader2.Core.Enums;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Orders.UserCases.Orders.BOM.CompanyEmail.CompanyEmail.Queries;
using GlobalTrader2.Orders.UserCases.Orders.BOM.CSVExportLog.Commands;
using GlobalTrader2.Orders.UserCases.Orders.BOM.ExportToCSVForClientList.Commands;
using GlobalTrader2.Orders.UserCases.Orders.BOM.MailLog.Commands;
using Microsoft.Extensions.Configuration;
using System.Net.Mail;
using System.Net.Mime;
using System.Text;

namespace GlobalTrader2.Orders.UserCases.Orders.BOM.NotifyMessageSupplier.Commands
{
    public class CreateNotifyMessageSupplierHandler : IRequestHandler<CreateNotifyMessageSupplierCommand, BaseResponse<int>>
    {
        private readonly IMediator _mediator;
        private readonly IEmailService _emailService;
        public CreateNotifyMessageSupplierHandler(IMediator mediator, IEmailService emailService)
        {
            _mediator = mediator;
            _emailService = emailService;
        }

        public async Task<BaseResponse<int>> Handle(CreateNotifyMessageSupplierCommand request, CancellationToken cancellationToken)
        {
            ArgumentNullException.ThrowIfNull(request);

            var response = new BaseResponse<int>();

            var companyEmailsReq = await _mediator.Send(new GetAllCompanyEmailQuery()
            {
                CompanyIds = [.. request.ToCompanyIdsArray!.Select(x => x)],
            }, cancellationToken);
            var companyEmails = companyEmailsReq.Data!.ToList();

            string sectionName = request.ReportNo == (int)Report.RequirementWithBOM ? "BOM" : "PurchaseHub";
            await _mediator.Send(new CreateMailLogCommand()
            {
                CompanyName = string.Join(",", companyEmails.Select(x => x.CompanyName)),
                DocumentID = request.Id,
                Type = sectionName

            }, cancellationToken);
            int srNo = 0;

            foreach (var companyId in request.ToCompanyIdsArray)
            {
                if (!string.IsNullOrEmpty(request.ToLoginsArray.ElementAtOrDefault(srNo)))
                {
                    var email = request.ToLoginsArray.ElementAtOrDefault(srNo);
                    var companyInfo = companyEmails.FirstOrDefault(x => x.CompanyId == companyId);
                    var csvFileReq = await _mediator.Send(new CreateExportToCsvForClientListCommand()
                    {
                        ReportId = request.ReportNo,
                        Id = request.Id,
                        CurrencyCode = request.CurrencyCode,
                        CompanyId = companyId,
                        SrNo = srNo,
                        ClientId = request.ClientId,
                        LoginFullName = request.LoginFullName,
                        Resources = request.Resources
                    }, cancellationToken);
                    var csvFile = csvFileReq.Data;
                    await _mediator.Send(new CreateCsvExportLogCommand()
                    {
                        CompanyNo = companyId,
                        FileName = csvFile.FileName,
                        UpdatedBy = request.LoginID,
                        Section = sectionName,
                        SendMailGroupID = email,
                        PurchaseQuoteNo = request.Id,
                        Message = request.Message,
                        Subject = request.Subject,
                    }, cancellationToken);

                    var body = GetBodyMessage(request.Message, companyInfo!.CompanyName, request.LoginFullName);

                    using var attachmentStream = new MemoryStream(csvFile.File);
                    using var attachment = new Attachment(attachmentStream, csvFile.FileName, MediaTypeNames.Text.Csv);

                    await _emailService.TrySendEmailAsync(request.LoginEmail, [email], [request.LoginEmail], [], request.Subject, body, [request.ReplyTos], [attachment], cancellationToken);
                }
                srNo++;
            }

            response.Success = true;
            return response;
        }

        private static string GetBodyMessage(string body, string supplierName, string loginFullName)
        {
            StringBuilder strBuilder = new StringBuilder();
            strBuilder.Append("Dear " + supplierName + ",");
            strBuilder.Append("<br/><br/>");
            strBuilder.Append(body);
            strBuilder.Append("<br/><br/>");
            strBuilder.Append("Thanks,<br/>");
            strBuilder.Append(loginFullName + ",");
            return strBuilder.ToString();
        }
    }
}
