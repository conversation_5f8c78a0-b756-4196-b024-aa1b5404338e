﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Dto.PurchaseOrder;

namespace GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetSupplierApprovalPoHistory
{
    public record GetSupplierApprovalPoHistoryQuery(int PurchaseOrderId, CultureInfo CultureInfo) : IRequest<BaseResponse<GetApprovalStatusDataResponse<SupplierApprovalHistoryDto>>>
    {
    }
}
