﻿using FluentValidation;
using GlobalTrader2.Aggregator.UseCases.FileUpload.Commands.InsertSourcingImage;
using GlobalTrader2.Core.Helpers;

namespace GlobalTrader2.Orders.UserCases.Orders.Sourcing.Commands.InsertSourcingImage
{
    public class InsertSourcingImageValidator : AbstractValidator<InsertSourcingImageCommand>
    {
        public InsertSourcingImageValidator()
        {
            RuleFor(x => x.SourcingNo).NotEmpty().NotNull();
            RuleFor(x => x.ImageName)
                .NotEmpty()
                .NotNull()
                .MaximumLength(250);

            RuleFor(x => Functions.ReplaceLineBreaks(x.Caption, "⏎"))
                .MaximumLength(200);
        }
    }
}