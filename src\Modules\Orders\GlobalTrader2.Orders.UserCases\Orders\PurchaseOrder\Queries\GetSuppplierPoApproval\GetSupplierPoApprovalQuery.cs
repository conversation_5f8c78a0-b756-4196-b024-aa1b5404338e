﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Dto.PurchaseOrder;

namespace GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetSuppplierPoApproval
{
    public record GetSupplierPoApprovalQuery(int PurchaseOrderNo, int LoginId, int ClientId, CultureInfo CultureInfo) : IRequest<BaseResponse<GetApprovalStatusDataResponse<SupplierPoApprovalDto>>>
    {
    }
}
