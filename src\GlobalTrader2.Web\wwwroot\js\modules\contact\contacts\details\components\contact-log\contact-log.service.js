export class ContactLogService {
    static #baseURL = '';

    static setBaseURL(url) {
        this.#baseURL = url;
    }

    static async addNewContactLog($form) {
        return await GlobalTrader.FormHelper.sendPostRequestAsync(this.#baseURL + '/contact-log', $form);
    }

    static async editContactLog(contactLogId, $form) {
        return await GlobalTrader.FormHelper.sendPutRequestAsync(this.#baseURL + `/contact-log/${contactLogId}`, $form);
    }

    static getSearchContactLogURL() {
        return '/api/contact/all-companies/contact-log/search'; 
    }
}
