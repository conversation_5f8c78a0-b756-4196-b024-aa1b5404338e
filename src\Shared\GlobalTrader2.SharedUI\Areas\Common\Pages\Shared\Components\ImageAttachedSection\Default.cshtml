﻿@using GlobalTrader2.Core.Enums
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.ImageAttachedSection
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.RemoveFileDialog
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.UploadMediaDialog
@using GlobalTrader2.SharedUI.Helper
@using GlobalTrader2.SharedUI.Models
@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.Extensions.Localization

@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IViewLocalizer _localizer
@model ImageAttachedSectionViewModel
@{
    var sectionBoxClasses = HtmlHelperExtensions.GetSectionBoxClass();
    var headerClasses = HtmlHelperExtensions.GetHeaderClass();
}

<div id="@Model.SectionId-wrapper" class="mb-3">
    <div id="@Model.SectionId-box" class="@sectionBoxClasses" data-loading="false">
        <h2 class="@headerClasses">
            <span class="section-box-title">
                @_localizer[Model.SectionBoxTitle]
            </span>
            @if (Model.CanAdd)
            {
                <span class="section-box-subtitle">
                    <span>@_localizer["Max Document File"]</span>
                    <span id="max-document-file"></span>
                    <span> - </span>
                    <span>@_localizer["Max Document Size"]</span>
                    <span id="document-size"></span>
                </span>
            }
            <span id="@Model.SectionId-button-group" class="section-box-button-group">
                @if (Model.CanAdd)
                {
                    <span class="d-flex gap-2">
                        <button class="btn btn-primary" id="@Model.SectionId-upload-btn">
                            <img src="~/img/icons/plus.svg" alt="@_commonLocalizer["Upload"]" />
                            <span>@_commonLocalizer["Upload"]</span>
                        </button>
                    </span>
                }
            </span>
        </h2>
          <div id="@Model.SectionId-content">
              <div id="@Model.SectionId-file-upload" class="mb-2"></div>
              <div class="text-center" id="@Model.SectionId-no-data-message" style="display:none">
                  <i>@_localizer[Model.NoDataMessage]</i>
              </div>
              <div class="card-area">
                  <div class="row g-2" id="@Model.SectionId-list">
                  </div>
              </div>
          </div>
    </div>
</div>

@if (Model.CanAdd && !Model.IsReadOnly) 
{
    @await Component.InvokeAsync(nameof(UploadMediaDialog), Model.UploadMediaDialog)
}

@if (Model.CanDelete && !Model.IsReadOnly)
{
    @await Component.InvokeAsync(nameof(RemoveFileDialog), Model.RemoveFileDialog)
}
