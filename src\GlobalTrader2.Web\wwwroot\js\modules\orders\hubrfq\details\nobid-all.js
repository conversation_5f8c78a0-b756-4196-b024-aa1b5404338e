import { LiteFormDialog } from "../../../../components/base/lite-form-dialog.js?v=#{BuildVersion}#";

export class NoBidAllHandler {
    constructor() {
        this.form = null;
        this.init();
    }

    init() {
        const self = this; // Store reference to access form instance
        this.form = new LiteFormDialog("#hubrfq-no-bid-all-dialog", {
            width: '800px',
            closeWhenSuccess: true,
            url: "/api/orders/bom/bom-bid-requirement",
            buttons: [
                { name: "save", icon: "check", alt: "yes", display: 'Yes' },
                { name: "cancel", icon: "xmark", alt: "no", display: 'No' }
            ],
            validationRules: {
                bidNotes: {
                    required: true,
                }
            },
            errorPlacement: function (error, element) {
                const errorSummary = $("#hubrfq-no-bid-all-dialog .form-error-summary");
                error.insertAfter(element);
                errorSummary.show();
            },
            body: function () {
                const data = self.form.getFormData();
                return {
                    bomId: data.id,
                    bomCode: data.code,
                    bomName: data.name,
                    bomCompanyName: data.company,
                    bomCompanyNo: data.companyNo,
                    salesmanNo: data.reqSalesPerson ? data.reqSalesPerson.toString().replace(/\|\|+$/, '').split("||") : [],
                    bidNotes: data.bidNotes
                };
            }
        });

        this.form.on('dialogopen', () => {
            $("#hubrfq-no-bid-all-dialog .form-error-summary").hide();
        });

        this.form.$form.on('closedWithSuccessedResponse.mf', (e, { response }) => {
            $('#no-bid-btn').prop('disabled', true);
        });
    }
}
