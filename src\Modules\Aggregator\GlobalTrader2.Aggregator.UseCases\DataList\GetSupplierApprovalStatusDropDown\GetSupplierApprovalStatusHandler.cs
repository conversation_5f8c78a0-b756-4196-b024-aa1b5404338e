﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Dto.Base;

namespace GlobalTrader2.Aggregator.UseCases.DataList.GetSupplierApprovalStatusDropDown
{
    public class GetSupplierApprovalStatusHandler : IRequestHandler<GetSupplierApprovalStatusDropDownQuery, BaseResponse<List<DropDownDto>>>
    {
        private readonly IBaseRepository<SupplierApprovalStatusReadModel> _repository;
        public GetSupplierApprovalStatusHandler(IBaseRepository<SupplierApprovalStatusReadModel> repository)
        {
            _repository = repository;
        }
        public async Task<BaseResponse<List<DropDownDto>>> Handle(GetSupplierApprovalStatusDropDownQuery request, CancellationToken cancellationToken)
        {
            var queryResult = await _repository.SqlQueryRawAsync($"{StoredProcedures.Get_dropdown_SupplierApprovalStatus}", [], commandTimeout: 30);
            var response = new BaseResponse<List<DropDownDto>>
            {   Success = true,
                Data = queryResult.Select(x => new DropDownDto
                {
                    Id = x.SupplierApprovalStatusId,
                    Name = x.Name
                }).ToList()
            };
            return response;
        }
    }

}
