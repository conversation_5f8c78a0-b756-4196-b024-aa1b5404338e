﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Dto.PurchaseOrder
{
    public class SupplierApprovalTradeReferenceDto
    {
        public int SupplierApprovalId { get; set; }
        public int SupplierId { get; set; }
        public string? SupplierName { get; set; }
        public string? FranchiseweblinkOrEvidence { get; set; }
        public string? TradeReferenceOne { get; set; }
        public bool? IsPDFAvalableOne { get; set; }
        public string? TradeReferenceTwo { get; set; }
        public bool? IsPDFAvalableTwo { get; set; }
        public string? TradeReferenceThree { get; set; }
        public bool? IsPDFAvalableThree { get; set; }
        public int EvidenceCount { get; set; }
        public int TradeRefOneCount { get; set; }
        public int TradeRefTwoCount { get; set; }
        public int TradeRefThreeCount { get; set; }
        public int DevicePictureCount { get; set; }
        public int ManufacturerPictureCount { get; set; }
        public int TraceabilityPictureCount { get; set; }
        public int CountryOnHighRisk { get; set; }
        public bool IsPOApproved { get; set; }
        public int SupplierApprovalStatus { get; set; }
        public int BuyerId { get; set; }
        public string? Status { get; set; }
        public string? SupplierAdvisoryNotes { get; set; }  
        public string? QualityApproveDate { get; set; }
        public string? LineManagerApproveDate { get; set; }
        public bool IsQualityGroupUser { get; set; } 
        public string? WarrantyPeriod { get; set; }
        public int? ClientNo { get; set; }
        public string? ApprovedDate { get; set; }
        public string? LineManagerComment { get; set; }
        public string? QualityComment { get; set; }
        public string? CommentText { get; set; }
        public string? SupplierType { get; set; }
    }
}
