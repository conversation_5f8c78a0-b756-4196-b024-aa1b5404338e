﻿using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;

namespace GlobalTrader2.Core.Domain.Entities
{
    [Table("vwBOM")]
    [Keyless]
    public class VwBomModel
    {
        public int? BOMId { get; set; }
        public int? Clientno { get; set; }
        public string? BOMName { get; set; }
        public string? Notes { get; set; }
        public string? BOMCode { get; set; }
        public bool? Inactive { get; set; }
        public int? CompanyId { get; set; }
        public string? CompanyName { get; set; }
        public int? ContactId { get; set; }
        public string? ContactName { get; set; }
        public int? RequestToPOHubBy { get; set; }
        public DateTime? DateRequestToPOHub { get; set; }
        public string? Status { get; set; }
        public int? ReleaseBy { get; set; }
        public DateTime? DateRelease { get; set; }
        public DateTime? DLUP { get; set; }
        public int? UpdatedBy { get; set; }
        public int? StatusValue { get; set; }
        public string? CurrencyCode { get; set; }
        public int? CurrencyId { get; set; }
        public string? Currency_Code { get; set; }
        public int? CompanyNo { get; set; }
        public string? CurrentSupplier { get; set; }
        public DateTime? QuoteRequired { get; set; }
        public int? UpdateByPH { get; set; }
        public double? TotalBomLinePrice { get; set; }
        public bool? AS9120 { get; set; }
        public string? Releasedby { get; set; }
        public string? Requestedby { get; set; }
        public string? AssignTo { get; set; }
        public int? Contact2Id { get; set; }
        public string? Contact2Name { get; set; }
        public int? AS6081Item { get; set; }
        public bool? IsGroupAssignment { get; set; }
        public string? AssigneeIds { get; set; }
    }
}
