import { SupplierApprovalsService } from '../supplier-approvals.service.js?v=#{BuildVersion}#';
import { SupplierApprovalTableManager } from './supplier-approval-table.js?v=#{BuildVersion}#';
import { SupplierApprovalTableConfiguration } from '../config/supplier-approval-table-configuration.js?v=#{BuildVersion}#';
import { EventEmitter } from '../../../../../../../components/base/event-emmiter.js?v=#{BuildVersion}#';
import { ResizeDatatableEvents } from '../../../../../../../components/base/resize-lite-datatable/resize-lite-datatable-events.constanst.js?v=#{BuildVersion}#';
import { SupplierApprovalTabEvent } from '../config/supplier-approval-tab-constant.js?v=#{BuildVersion}#';

export class SupplierApprovalTabManager extends EventEmitter{ 
    constructor(purchaseOrderId, tabLevel) {
        super();
        this.purchaseOrderId = purchaseOrderId;
        this.tabLevel = tabLevel;
        this.supplierApprovalsService = new SupplierApprovalsService();
        this.data = [];
        this.tableManager = null;
    }

    async initialize() {
        await this._getTableData();
        this.tableManager = new SupplierApprovalTableManager(SupplierApprovalTableConfiguration[this.tabLevel].tableId, this.data, this.tabLevel);
    }
    
    initializeTable(){
        this.tableManager.init();
        this.tableManager.setUpTableEventListeners();
    }

    async refreshTabData(){
        await this._getTableData();  
        let selectedRowId = null;
        const selectedRow = this.tableManager.datatable.row({ selected: true }).data();
        if (selectedRow) {
            selectedRowId = selectedRow.supplierApprovalId;
        }
        this.tableManager.renderDatatable(this.data, selectedRowId);
    }
    
    setUpEventListeners(){
        this.tableManager.on(ResizeDatatableEvents.SELECT_DESELECT, (selectedRows) => {
           this.triggerAsync(ResizeDatatableEvents.SELECT_DESELECT, selectedRows);
        });
    }

    async _getTableData(){
        const response = await this.supplierApprovalsService.getTabDataAsync(this.purchaseOrderId, this.tabLevel);
        if(response.success) {
            this.data = response.data.lines;
        }else{
            this.data = [];
        }
        this.trigger(SupplierApprovalTabEvent.ON_DATA_LOADED, this.data);
    }
    
}