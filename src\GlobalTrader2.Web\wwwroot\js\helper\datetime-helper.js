﻿GlobalTrader.DatetimeHelper = (function () {
    const toDayTextLocalizer = window.localizedStrings.Today;
    const yesterdayTextLocalizer = window.localizedStrings.Yesterday;
    const dayAgoTextLocalizer = window.localizedStrings.XDaysAgo;
    // input expected ISO 8601 format
    function formatDateShortTime(datetime, culture) {
        if (culture == 'en-GB') {
            const dateObj = new Date(datetime);

            const day = String(dateObj.getDate()).padStart(2, '0');
            const month = String(dateObj.getMonth() + 1).padStart(2, '0');
            const year = dateObj.getFullYear();
            const hours = String(dateObj.getHours()).padStart(2, '0');
            const minutes = String(dateObj.getMinutes()).padStart(2, '0');

            return `${day}/${month}/${year} ${hours}:${minutes}`;
        }
        else {
            console.error("Expected right culture input");
        }
    }
    function convertCSharpToJqueryPattern(csharpPattern) {
        const mapping = {
            "d": "d",
            "dd": "dd",
            "ddd": "D",
            "dddd": "DD",
            "M": "m",
            "MM": "mm",
            "MMM": "M",
            "MMMM": "MM",
            "yy": "y",
            "yyyy": "yy"
        };

        return csharpPattern.replace(/d{1,4}|M{1,4}|y{2,4}/g, match => mapping[match] || match);
    }

    function splitDateTime(dateTimeString) {
        const dateTime = new Date(dateTimeString);
        const year = dateTime.getFullYear();
        const month = dateTime.getMonth() + 1;
        const day = dateTime.getDate();
        const hour = dateTime.getHours();
        const minute = dateTime.getMinutes();

        return {
            date: `${day.toString().padStart(2, '0')}/${month.toString().padStart(2, '0')}/${year}`,
            hour: hour.toString().padStart(2, '0'),
            minute: minute.toString().padStart(2, '0'),
        };
    }

    function getShortDateFormat() {
        return convertCSharpToJqueryPattern(appInfo.cultureInfo.datetimeFormat.shortDate)
    }

    return {
        formatDate(date, separatingCharacter = '/') {
            let day = date.getDate();
            let month = date.getMonth() + 1;
            let year = date.getFullYear();
            day = day < 10 ? '0' + day : day;
            month = month < 10 ? '0' + month : month;

            return `${day}${separatingCharacter}${month}${separatingCharacter}${year}`;
        },

        formatDateShortTimeCulture(datetime) {
            return formatDateShortTime(datetime, appInfo.cultureInfo.culture);
        },
        convertCSharpToJqueryPattern: convertCSharpToJqueryPattern,
        getShortDateFormat: getShortDateFormat,
        splitDateTime: splitDateTime,

        formatDaysAgo(daysAgoInt) {
            let strOut = "&nbsp;";
            if (daysAgoInt != null) {
                switch (daysAgoInt) {
                    case 0:
                        strOut = toDayTextLocalizer;
                        break;
                    case 1:
                        strOut = yesterdayTextLocalizer;
                        break;
                    default:
                        strOut = dayAgoTextLocalizer.replace("{0}", daysAgoInt);
                        break;
                }
            }
            return strOut;
        },

        oneWeekAgo() {
            const dtm = new Date();
            dtm.setDate(dtm.getDate() - 7);
            return GlobalTrader.DatetimeHelper.formatDate(dtm);
        }
    };

})();