﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Dto.PurchaseOrder;

namespace GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.SupplierApprovalTradeReference
{
    public record GetSupplierApprovalTradeReferenceQuery(int PurchaseOrderId, int LoginId, int ClientId) : IRequest<BaseResponse<GetApprovalStatusDataResponse<SupplierApprovalTradeReferenceDto>>> 
    { 
    }
}
