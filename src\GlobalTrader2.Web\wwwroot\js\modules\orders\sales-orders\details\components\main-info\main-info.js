﻿import { ButtonHelper } from '../../../../../../helper/button-helper.js?v=#{BuildVersion}#'
import { EditSalesOrderMainInfoManager } from './edit-main-info.js?v=#{BuildVersion}#';
import { PayByCreditCardDialogManager } from './pay-by-credit-card-dialog.js?v=#{BuildVersion}#';
import { CloseSalesOrderDialog } from './components/close-sales-order.js?v=#{BuildVersion}#';
import { ConfirmSalesOrderSentDialog } from './components/confirm-so-sent.js?v=#{BuildVersion}#'; 
import { openPopup } from '../../../../../../helper/url-helper.js?v=#{BuildVersion}#';
import { EventEmitter } from '../../../../../../components/base/event-emmiter.js?v=#{BuildVersion}#';
import { NotifySalesOrderDialog } from './components/notify-sales-order.js?v=#{BuildVersion}#';
import { CreateIPOManager } from '../lines/components/create-ipo-so-line.js?v=#{BuildVersion}#';
import { CreateInternalPurchaseOrderFrom } from '../../constants/create-ipo-from.js?v=#{BuildVersion}#';
export class SalesOrdersMainInfoManager extends EventEmitter {
    constructor(
        {
            salesOrderId,
            companyId,
            globalLoginClientNo,
            refreshCallback = (data) => { },
            onEditMainInfoSuccess = () => { }
        }) {
        super();
        this.salesOrderId = salesOrderId;
        this.companyId = companyId;
        this.globalLoginClientNo = globalLoginClientNo;
        this.salesOrdersMainInfo = null;
        this.onEditMainInfoSuccess = onEditMainInfoSuccess;
        this.$salesOrdersMainInfoWrapper = $("#sales-orders-main-info-wrapper");
        this.$salesOrdersMainInfomationBox = $("#sales-orders-main-information-box");
        this._$addToDoItem = $("#add-todo-task");
        this._$viewToDoItem = $("#view-todo-task");
        this._$addToDoDialog = $("#add-to-do-dialog");
        this._$payByCreditCardBtn = $('#pay-by-credit-card-btn');
        this.soPromissedDatePassedDialog = $("#so-promissed-date-passed-dialog");
        this._blnFirstMessagePop = true;
        this.$editMainInfoButton = this.$salesOrdersMainInfomationBox.find("#edit-main-info-button");
        this.$closeSoButton = this.$salesOrdersMainInfomationBox.find("#close-sales-order-btn");
        this.$confirmSoSentButton = this.$salesOrdersMainInfomationBox.find("#confirm-so-sent-btn");
        this.$viewTreeButton = this.$salesOrdersMainInfomationBox.find("#so-detail-view-tree-btn");
        this.$notifySoButton = this.$salesOrdersMainInfomationBox.find("#so-notify-btn");
        this.$createIpoButton = this.$salesOrdersMainInfomationBox.find("#main-info-create-ipo-btn");
        this.closeSODialog = new CloseSalesOrderDialog();
        this.editSalesOrderMainInfoManager = new EditSalesOrderMainInfoManager({
            salesOrderId: this.salesOrderId,
            companyId: this.companyId,
            globalLoginClientNo: this.globalLoginClientNo,
            successCallback: async () => {
                await this.refreshSectionBox();
                if (this.onEditMainInfoSuccess) this.onEditMainInfoSuccess();
                this.trigger('editSuccess');
            }
        });
        this.payByCreditCardDialogManager = new PayByCreditCardDialogManager(this.salesOrderId);
        this.confirmSalesOrderSentDialog = new ConfirmSalesOrderSentDialog(this.salesOrderId);
        this.notifySalesOrderDialog = new NotifySalesOrderDialog(this.salesOrderId);
        this._createIpo = new CreateIPOManager(this.salesOrderId, CreateInternalPurchaseOrderFrom.MainInfo, "create-ipo-main-info-dialog", "create-ipo-main-info-form");
    }

    async initialize() {
        this.setupSalesOrdersMainInfoSectionBox();
        this.$salesOrdersMainInfomationBox.section_box("option", "loading", true);
        await this.getSalesOrdersMainInfo();
        this.soPromissedDatePassedDialog.dialog({
            maxHeight: $(window).height(),
            width: "25vw",
            close: function () {

            },
            buttons: [
                {
                    text: window.localizedStrings.ok,
                    class: 'btn btn-primary fw-normal',
                    html: `<img src="/img/icons/check.svg" alt="${window.localizedStrings.ok}">${window.localizedStrings.ok.toUpperCase()}`,
                    click: () => {
                        this.soPromissedDatePassedDialog.dialog("close");
                    },
                },
            ]
        });
        this.bindingSalesOrdersMainInfo();
        this.bindOnClickEvents();
        this._bindViewToDoItemTaskCount();
        this._onSaveAddTaskComplete();
        this.$salesOrdersMainInfomationBox.section_box("option", "loading", false);
        this.editSalesOrderMainInfoManager.initialize();
        this.closeSODialog.initialize();
        this.closeSODialog.on('onCloseSuccess', async () => {
            window.location.reload();
        });

        this.payByCreditCardDialogManager.initialize();
        this._bindPayByCreditCardClick();
        this.confirmSalesOrderSentDialog.initialize();
        this.confirmSalesOrderSentDialog.on('onConfirmSuccess', async () => {
            await this.refreshSectionBox();
        });
        this.notifySalesOrderDialog.initialize();
        this._createIpo.initialize();
        this._createIpo.setSelectedLine([0]);
    }

    setupSalesOrdersMainInfoSectionBox() {
        this.$salesOrdersMainInfomationBox.section_box({
            onRefreshClick: async (event, ui) => {
                await this.refreshSectionBox();
                this.$salesOrdersMainInfomationBox.trigger('refresh');
            }
        });
    }

    bindOnClickEvents() {
       this._bindAddToDoItemClick();
       this._bindViewToDoItemClick();
        this.$editMainInfoButton.on("click", async () => {
            this.editSalesOrderMainInfoManager.openDialog();
        })

        this.$closeSoButton.on("click", async () => {
            if (this.closeSODialog == null) return;
            this.closeSODialog.setSalesOrderInfo(this.salesOrdersMainInfo);
            this.closeSODialog.openDialog();
        });

        this.$confirmSoSentButton.on("click", async () => {
            if (this.confirmSalesOrderSentDialog == null) return;
            this.confirmSalesOrderSentDialog.setVerificationToken(this.$confirmSoSentButton.data('antiforgerytoken'))
            this.confirmSalesOrderSentDialog.openDialog();
        });

        this.$viewTreeButton.on("click", () => {
            this._openViewTreeWindows(this.salesOrdersMainInfo.salesOrderId, this.salesOrdersMainInfo.salesOrderNumber)
        }); 
        this.$notifySoButton.on("click", async () => {
            if (this.notifySalesOrderDialog == null) return;          
            this.notifySalesOrderDialog.openDialog();
        });
        this.$createIpoButton.on("click", () => {
            if (this._createIpo == null) return;
            this._createIpo.bindingPreferredWarehouse(this.salesOrdersMainInfo.preferredWarehouseName, this.salesOrdersMainInfo.preferredWarehouseNo);
            this._createIpo.openDialog();
        });
    }

    async getSalesOrdersMainInfo() {
        let response = await GlobalTrader.ApiClient.getAsync(`/orders/sales-order-detail/${this.salesOrderId}/main-info`);
        if (response.success) {
            this.salesOrdersMainInfo = response.data;
            $("#hidSalesOrderStatus").val(this.salesOrdersMainInfo.statusNo)
            this.trigger('salesOrdersMainInfoLoaded', this.salesOrdersMainInfo);
        } else {
            console.log(response.errors);
        }
    }

    bindingSalesOrdersMainInfo() {
        if (!this.salesOrdersMainInfo) return;
        $("#isCurrencyInSameFaimly").val(this.salesOrdersMainInfo.isCurrencyInSameFaimly);
        $("#isSOAuthorised").val(this.salesOrdersMainInfo.authorisedBy != null);
        $("#oldFreight").val(this.salesOrdersMainInfo.freightVal);
        $("#hidCurrencyCode").val(this.salesOrdersMainInfo.currencyCode);
        $("#hidCurrencyNo").val(this.salesOrdersMainInfo.currencyNo);
        $("#ctlDateOrdered").val(this.salesOrdersMainInfo.dateOrdered);
        $("#hidCustomer").val(this.salesOrdersMainInfo.customerName);
        $("#hidSalesOrderNumber").val(this.salesOrdersMainInfo.salesOrderNumber);

        if (this.salesOrdersMainInfo.redFlagged) {
            if (this._blnFirstMessagePop == true) {
                $("#so-serial-number").text(this.salesOrdersMainInfo.lineSerialNo);
                this.soPromissedDatePassedDialog.dialog("open");
            }
            this._blnFirstMessagePop = false;
        }
        if (this.salesOrdersMainInfo.isDifferCurrencyFromCustomer) {
            $("#warning-section").removeClass("d-none");
        }
        else {
            $("#warning-section").addClass("d-none");
        }

        if (this.salesOrdersMainInfo.isPaidByCreditCard) {
            $("#isPaidByCreditCard").removeClass("d-none");
        } else {
            $("#isPaidByCreditCard").addClass("d-none");
        }
        if (this.salesOrdersMainInfo.aS6081_Text.toLowerCase() == "yes") {
            $("#AS6081_Field").css("background-color", "yellow"); 
        } else {
            $("#AS6081_Field").css("background-color", ""); 
        }
        
        const htmlRawFieldNames = [
            "internalNotes",
            "customerNotes",
            "purchasingNotes"
        ];

        // Update check box value
        this.$salesOrdersMainInfoWrapper.find("input[type=checkbox]").toArray().forEach(input => {
            const fieldName = $(input).data("field");
            $(input).prop("checked", this.getPropertyCaseInsensitive(this.salesOrdersMainInfo, fieldName))
                .trigger('change');
        });

        // Update text value
        this.$salesOrdersMainInfoWrapper.find("span[data-field]").toArray().forEach(element => {
            const fieldName = $(element).data("field");
            let fieldValue = this.getPropertyCaseInsensitive(this.salesOrdersMainInfo, fieldName);

            if (htmlRawFieldNames.includes(fieldName)) {
                $(element).html(GlobalTrader.StringHelper.setCleanTextValue(fieldValue, true));
            } else {
                $(element).text(GlobalTrader.StringHelper.setCleanTextValue(fieldValue));
            }
        });

        const contactButtonHtml = ButtonHelper.nubButton_Contact(this.salesOrdersMainInfo.contactNo, this.salesOrdersMainInfo.contactName);
        this.$salesOrdersMainInfomationBox.find(`#sales-order-form-buyer`).html(contactButtonHtml);
        const companyButtonHtml = `${ButtonHelper.createNubButton(GlobalTrader.PageUrlHelper.Get_URL_Company(this.salesOrdersMainInfo.customerNo), this.salesOrdersMainInfo.customerName)}
                ${this.salesOrdersMainInfo.companyOnStop ? ButtonHelper.createOnStopIcon() : ""}
                ${this.salesOrdersMainInfo.customerAdvisoryNotes.length > 0 ? ButtonHelper.createAdvisoryNotesIcon(this.salesOrdersMainInfo.customerAdvisoryNotes) : ""}`
        this.$salesOrdersMainInfomationBox.find(`#sales-order-form-customer-name`).html(companyButtonHtml);

        // update confirm sales order sent button 
        this.$confirmSoSentButton.prop('disabled', this.salesOrdersMainInfo.sentOrder);
    }

    // Get value by property name, case insensitive
    getPropertyCaseInsensitive(obj, key) {
        key = key.toLowerCase();
        for (let prop in obj) {
            if (prop.toLowerCase() === key) {
                return obj[prop];
            }
        }
        return undefined; // not found
    }

    async refreshSectionBox() {
        this.$salesOrdersMainInfomationBox.section_box("option", "loading", true);
        await this.getSalesOrdersMainInfo();
        this.bindingSalesOrdersMainInfo();
        this._bindViewToDoItemTaskCount();
        this.$salesOrdersMainInfomationBox.section_box("option", "loading", false);
    }

    updateCreateIpoButton(isCheckboxEnabled) {
        this.$createIpoButton.prop('disabled', !isCheckboxEnabled);
    }

    _bindAddToDoItemClick() {
        this._$addToDoItem.on("click", async (event) => {
            const $event = $(event.currentTarget);
            $event.data('sales-order-no', this.salesOrdersMainInfo.salesOrderId);
            $event.data('sales-order-number', this.salesOrdersMainInfo.salesOrderNumber);
            $event.data('companyName', this.salesOrdersMainInfo.customerName);
            $event.data('category-type', SALES_ORDER_TASK_CATEGORY);
            openSpecificCategoryToDoDialog(event);
        });
    }

    _onSaveAddTaskComplete() {
        this._$addToDoDialog.on('addTodoSuccess', (e) => {
            this.refreshSectionBox();              
        });
    }

    _bindViewToDoItemTaskCount() {
        this._$viewToDoItem.find('span').text(`${this.salesOrdersMainInfo.taskCount} ${localizedStrings.tasks}`);
        if (this.salesOrdersMainInfo.hasUnFinishedTask > 0) {
            this._$viewToDoItem.find('span').css('color', 'red');
            this._$viewToDoItem.attr('title', localizedStrings.needToCompleteOpenTasks);
        }
        else {
            this._$viewToDoItem.find('span').css('color', 'white');
            this._$viewToDoItem.attr('title', localizedStrings.viewTask);
        }
        
    }

    _bindViewToDoItemClick() {
        this._$viewToDoItem.on("click", async (event) => {
            window.location.href = `/Profile/ToDo?so=${this.salesOrdersMainInfo.salesOrderNumber}&Category=4`;
        });
    }
    _bindPayByCreditCardClick() {
        this._$payByCreditCardBtn.on("click", async (event) => {
            this.payByCreditCardDialogManager.openDialog();
        })
    }

    _openViewTreeWindows(salesOrderId, salesOrderNumber) {
        openPopup(ButtonHelper.URL_All_Document(salesOrderId, "SO", salesOrderNumber), "winTreeView", 450)
    }
}
