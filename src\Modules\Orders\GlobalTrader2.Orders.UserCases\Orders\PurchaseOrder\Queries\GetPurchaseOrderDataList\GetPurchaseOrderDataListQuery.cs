﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Dto.PurchaseOrder;
using GlobalTrader2.Dto.PurchaseRequisition;

namespace GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetPurchaseOrderDataList
{
    public class GetPurchaseOrderDataListQuery : IRequest<BaseResponse<List<PurchaseOrderDataListDto>>>
    {
        public GetPurchaseOrderPagingRequest Data { get; set; } = new GetPurchaseOrderPagingRequest();
        public PurchaseOrderFilter? Filters { get; set; }
        public CultureInfo CultureInfo { get; set; }
        public bool IsPOHub { get; set; }

    }
}
