using GlobalTrader2.Dto.PurchaseOrderLine;
using GlobalTrader2.Orders.UseCases.PurchaseOrderLine.Models;

namespace GlobalTrader2.Orders.UseCases.Orders.POLine.Queries.GetPurchaseOrderLineDetails
{
    public class GetPurchaseOrderLineDetailsHandler : IRequestHandler<GetPurchaseOrderLineDetailsQuery, BaseResponse<PurchaseOrderLineDetailsDto>>
    {
        private readonly IBaseRepository<PurchaseOrderLineDetailsReadModel> _repository;
        private readonly IMapper _mapper;

        public GetPurchaseOrderLineDetailsHandler(IBaseRepository<PurchaseOrderLineDetailsReadModel> repository, IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }

        public async Task<BaseResponse<PurchaseOrderLineDetailsDto>> Handle(GetPurchaseOrderLineDetailsQuery request, CancellationToken cancellationToken)
        {
            var parameters = new List<SqlParameter>
            {
                new("@PurchaseOrderLineId", request.PurchaseOrderLineId)
            };

            var result = await _repository.SqlQueryRawAsync($"{StoredProcedures.Select_PurchaseOrderLine} @PurchaseOrderLineId", [.. parameters]);
            var dto = result.Count > 0 ? _mapper.Map<PurchaseOrderLineDetailsDto>(result[0]) : null;
            

            return new BaseResponse<PurchaseOrderLineDetailsDto>
            {
                Success = true,
                Data = dto
            };
        }
    }
}