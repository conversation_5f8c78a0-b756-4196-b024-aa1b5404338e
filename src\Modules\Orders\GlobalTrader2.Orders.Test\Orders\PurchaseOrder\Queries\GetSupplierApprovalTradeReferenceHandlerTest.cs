﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Orders.UserCases.Commons.Mappings;
using GlobalTrader2.Orders.UserCases.Orders.BOM.CompanyAdvisoryNote.CompanyAdvisoryNote.Queries;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetIsQualityGroupUser;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.SupplierApprovalTradeReference;
using MediatR;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.PurchaseOrder.Queries
{
    public class GetSupplierApprovalTradeReferenceHandlerTest
    {
        private readonly Mock<IBaseRepository<SupplierApprovalTradeReferenceReadModel>> _repository;
        private readonly IMapper _mapper;
        private readonly Mock<IMediator> _mediator;

        public GetSupplierApprovalTradeReferenceHandlerTest()
        {
            _repository = new Mock<IBaseRepository<SupplierApprovalTradeReferenceReadModel>>();
            _mediator = new Mock<IMediator>();
            var mapperConfig = new MapperConfiguration(config => config.AddProfile<PurchaseOrderMapper>());
            _mapper = mapperConfig.CreateMapper();
        }

        [Fact]
        public async Task Handle_GetData_ShouldReturnSucces() { 
            // Arrange
            var response = new List<SupplierApprovalTradeReferenceReadModel>()
            { 
                new SupplierApprovalTradeReferenceReadModel(){ 
                    SupplierApprovalId = 1,
                    SupplierId = 1,
                    SupplierApprovalStatus = 1,
                    BuyerId = 1,
                    Status = "Approved",
                    SupplierName = "Test notes",
                    IsPDFAvalableOne = true,
                    IsPDFAvalableTwo = true,
                    IsPDFAvalableThree = true,
                    FranchiseweblinkOrEvidence = "Test",
                    TradeReferenceOne = "Test",
                    TradeReferenceTwo = "Test",
                    TrandeReferenceThree = "Test",
                    TradeRefOneCount = 1,
                    TradeRefTwoCount = 1,
                    TradeRefThreeCount = 1,
                    EvidenceCount = 1,
                }
            };
            _repository.Setup(repo => repo.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>())).ReturnsAsync(response);
            var handler = new GetSupplierApprovalTradeReferenceHandler(_repository.Object, _mapper, _mediator.Object);

            _mediator.Setup(me => me.Send(It.IsAny<GetCompanyAdvisoryNoteQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new Core.Bases.BaseResponse<string>());
            _mediator.Setup(me => me.Send(It.IsAny<GetIsQualityGroupUserQuery>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new Core.Bases.BaseResponse<bool>() { Data = true });
            // Act
            var result = await handler.Handle(new GetSupplierApprovalTradeReferenceQuery(1,1,1), CancellationToken.None);
            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.NotNull(result.Data);
        }
    }
}
