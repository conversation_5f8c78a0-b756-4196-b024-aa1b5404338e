﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Dto.PurchaseOrder;
using GlobalTrader2.Orders.UserCases.Orders.BOM.CompanyAdvisoryNote.CompanyAdvisoryNote.Queries;

namespace GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetSupplierApprovalPoHistory
{
    public class GetSupplierApprovalPoHistoryHandler : IRequestHandler<GetSupplierApprovalPoHistoryQuery, BaseResponse<GetApprovalStatusDataResponse<SupplierApprovalHistoryDto>>>
    {
        private readonly IBaseRepository<SupplierApprovalHistoryReadModel> _repository;
        private readonly IMapper _mapper;
        private readonly IMediator _mediator;

        public GetSupplierApprovalPoHistoryHandler(IBaseRepository<SupplierApprovalHistoryReadModel> repository, IMapper mapper, IMediator mediator)
        {
            _repository = repository;
            _mapper = mapper;
            _mediator = mediator;
        }

        public async Task<BaseResponse<GetApprovalStatusDataResponse<SupplierApprovalHistoryDto>>> Handle(GetSupplierApprovalPoHistoryQuery request, CancellationToken cancellationToken)
        {
            var readModels = await _repository.SqlQueryRawAsync(
                sql: $"{StoredProcedures.Select_SupplierApproval_PO_History} @PurchaseOrderId",
                parameters: [new SqlParameter("@PurchaseOrderId", SqlDbType.Int) { Value = request.PurchaseOrderId }]);
            List<SupplierApprovalHistoryDto> dtos = [];
            foreach (var readModel in readModels)
            {
                var dto = _mapper.Map<SupplierApprovalHistoryDto>(readModel);
                dto.SupplierAdvisoryNotes = await GetSupplierNotes(dto.SupplierId ?? 0);
                dto.ApprovedDated = Functions.FormatDate(readModel.ApprovedDated, request.CultureInfo);
                dtos.Add(dto);
            }
            return new BaseResponse<GetApprovalStatusDataResponse<SupplierApprovalHistoryDto>>()
            {
                Success = true,
                Data = new GetApprovalStatusDataResponse<SupplierApprovalHistoryDto>()
                {
                    Count = dtos.Count,
                    Lines = dtos.ToList()
                }
            };
        }

        private async Task<string> GetSupplierNotes(int supplierId)
        {
            if (supplierId < 0) return string.Empty;
            var advisoryNote = await _mediator.Send(new GetCompanyAdvisoryNoteQuery() { Id = supplierId });
            return advisoryNote.Data ?? string.Empty;
        }
    }
}
