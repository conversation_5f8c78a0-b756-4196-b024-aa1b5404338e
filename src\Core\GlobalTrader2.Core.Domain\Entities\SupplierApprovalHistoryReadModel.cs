﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Core.Domain.Entities
{
    public class SupplierApprovalHistoryReadModel
    {
        public int SupplierApprovalId { get; set; }
        public int? SupplierId { get; set; }
        public string? SupplierName { get; set; }
        public string? ApprovalStatus { get; set; }
        public DateTime? ApprovedDated { get; set; }
        public string? ApprovedBy { get; set; }
    }
}
