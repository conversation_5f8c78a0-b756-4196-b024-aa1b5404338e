﻿import { EventEmitter } from '../../../../../../../components/base/event-emmiter.js?v=#{BuildVersion}#';
import { SalesOrderDetailApiUrl } from '../../../../../../../config/api-endpoint-config.js?v=#{BuildVersion}#';
export class CloseSalesOrderDialog extends EventEmitter {
    constructor() {
        super();
        this.$dialog = $("#close-sales-order-dialog");
        this.$form = $("#close-sales-order-form");
        this.dialog = null;
        this.baseUrl = SalesOrderDetailApiUrl;
        this.salesOrderInfo = null;
        this.$yesButton = null;
        this.dialogLocalization = {
            closeSOAndResetLinesConfirm: closeSOLocalization.closeSOAndResetLinesConfirm,
            closeSOLineConfirm: closeSOLocalization.closeSOLineConfirm
        }
        this.soClientId = SODetailGeneralInfo.soClientId; // from SalesOrders/Details/index.cshtml
    }

    initialize() {
        this._setupDialog();
    }
    setSalesOrderInfo(salesOrderInfo) {
        this.salesOrderInfo = salesOrderInfo;
    }
    openDialog() {
        this.$dialog.dialog("open");
    }
    _setupDialog() {
        this.dialog = this.$dialog.dialog({
            width: "auto",
            height: "auto",
            maxHeight: 700,
            autoOpen: false,
            draggable: false,
            modal: true,
            open: async () => {
                $('.ui-dialog-titlebar-close').hide();
                this.$dialog.dialog("setLoading", true);
                this._bindDataToForm()
                this.$dialog.dialog("setLoading", false);
                const $input = this.$dialog.find('input:visible:enabled:first');
                if ($input.length) $input[0].focus();
            },
            buttons: [
                {
                    text: window.localizedStrings.yes,
                    class: 'btn btn-primary',
                    id: 'close-so-btn',
                    html: `<img src="/img/icons/check.svg" alt="Yes icon"/>${window.localizedStrings.yes}`,
                    click: async () => {
                        if (this.$form.valid()) {
                            const isResetQuantity = $('#close-sales-order-reset-quantity').is(':checked');
                            const result = confirm(isResetQuantity ? this.dialogLocalization.closeSOAndResetLinesConfirm : this.dialogLocalization.closeSOLineConfirm)
                            if (result) {
                                this.$dialog.find(".form-error-summary").hide();
                                this.$dialog.dialog("setLoading", true);
                                this.$dialog.parent().find(`.ui-dialog-buttonset button:has(img[alt="Yes icon"])`).prop('disabled', true);

                                const header = { "RequestVerificationToken": this.$form.find(':input[name="__RequestVerificationToken"]').val() }
                                const requestBody = {
                                    resetQuantity: isResetQuantity,
                                    clientId: this.soClientId
                                }
                                let response = await this._closeSalesOrder(this.salesOrderInfo.salesOrderId, header, requestBody)
                                if (!response?.success) {
                                    showToast("danger", response.message);
                                } else {
                                    showToast('success', window.localizedStrings.saveChangedMessage);
                                    this.trigger('onCloseSuccess');
                                }

                                this.$dialog.parent().find(`.ui-dialog-buttonset button:has(img[alt="Yes icon"])`).prop('disabled', false);
                                this.$dialog.dialog("setLoading", false);
                                this.$dialog.dialog("close");
                            }
                        } else {
                            this.$dialog.find(".form-error-summary").show();
                        }
                    }
                },
                {
                    text: window.localizedStrings.no,
                    class: 'btn btn-danger',
                    html: `<img src="/img/icons/xmark.svg" alt="No icon"/>${window.localizedStrings.no}`,
                    click: () => {
                        this.$dialog.dialog('close');
                    }
                }
            ],
            close: () => {
                this._resetDialog();
            }
        });
        this.$yesButton = this.$dialog.find("#close-so-btn");
    }

    async _closeSalesOrder(salesOrderId, header, requestBody) {
        let response = await GlobalTrader.ApiClient.putAsync(`${this.baseUrl}/${salesOrderId}/close`, requestBody, header);
        return response;
    }

    _bindDataToForm() {
        if (this.salesOrderInfo == null) return;
        this.$form.find("#close-sales-order-so-number").text(this.salesOrderInfo.salesOrderNumber);
        this.$form.find("#close-sales-order-customer").text(this.salesOrderInfo.customerName);
        this.$form.find("#close-sales-order-billing-address").text(this.salesOrderInfo.billingAddress);
    }

    _resetDialog() {
        this.$dialog.find(".form-error-summary").hide();
        this.$form[0].reset();
        this.$form.validate().resetForm();
        this.$form.find('.is-invalid').removeClass("is-invalid");
    }
} 