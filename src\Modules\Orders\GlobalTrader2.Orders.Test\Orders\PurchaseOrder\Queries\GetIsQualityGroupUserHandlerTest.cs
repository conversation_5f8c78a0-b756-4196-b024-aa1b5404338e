using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using FluentAssertions;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Core.Models;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetIsQualityGroupUser;
using Moq;
using Xunit;

namespace GlobalTrader2.Orders.Test.Orders.PurchaseOrder.Queries
{
    public class GetIsQualityGroupUserHandlerTest
    {
        private readonly Mock<IBaseRepository<Core.Domain.Entities.MailGroup>> _mockMailGroupRepository;
        private readonly Mock<IBaseRepository<MailGroupMember>> _mockMemberRepository;
        private readonly GetIsQualityGroupUserHandler _handler;

        public GetIsQualityGroupUserHandlerTest()
        {
            _mockMailGroupRepository = new Mock<IBaseRepository<Core.Domain.Entities.MailGroup>>();
            _mockMemberRepository = new Mock<IBaseRepository<MailGroupMember>>();
            _handler = new GetIsQualityGroupUserHandler(_mockMailGroupRepository.Object, _mockMemberRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnFalse_WhenMailGroupNotFound()
        {
            // Arrange
            var query = new GetIsQualityGroupUserQuery(1, 1);
            var mailGroups = new List<Core.Domain.Entities.MailGroup>().AsQueryable();

            _mockMailGroupRepository.Setup(r => r.ListAsQueryable()).Returns(mailGroups);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Data.Should().BeFalse();
        }

        [Fact]
        public async Task Handle_ShouldReturnFalse_WhenUserIsNotInMailGroup()
        {
            // Arrange
            var query = new GetIsQualityGroupUserQuery(1, 99); // User 99 is not in the group
            var mailGroup = new Core.Domain.Entities.MailGroup { MailGroupId = 123, Name = Core.Constants.MailGroup.QualityApproval, ClientNo = 1 };
            var mailGroups = new List<Core.Domain.Entities.MailGroup> { mailGroup }.AsQueryable();

            _mockMailGroupRepository.Setup(r => r.ListAsQueryable()).Returns(mailGroups);
            _mockMemberRepository.Setup(r => r.AnyAsync(It.IsAny<Expression<Func<MailGroupMember, bool>>>()))
                                 .ReturnsAsync(false);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Data.Should().BeFalse();
        }

        [Fact]
        public async Task Handle_ShouldReturnTrue_WhenUserIsInMailGroup()
        {
            // Arrange
            var query = new GetIsQualityGroupUserQuery(1, 1);
            var mailGroup = new Core.Domain.Entities.MailGroup { MailGroupId = 123, Name = Core.Constants.MailGroup.QualityApproval, ClientNo = 1 };
            var mailGroups = new List<Core.Domain.Entities.MailGroup> { mailGroup }.AsQueryable();

            _mockMailGroupRepository.Setup(r => r.ListAsQueryable()).Returns(mailGroups);
            _mockMemberRepository.Setup(r => r.AnyAsync(It.IsAny<Expression<Func<MailGroupMember, bool>>>()))
                                 .ReturnsAsync(true);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Data.Should().BeTrue();
        }
    }
}