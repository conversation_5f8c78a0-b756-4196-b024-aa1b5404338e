﻿@using GlobalTrader2.Dto.Sourcing
@using GlobalTrader2.SharedUI.Interfaces
@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.Extensions.Localization
@using Microsoft.Extensions.Hosting
@using GlobalTrader2.Core.Enums
@model AddSourcingResultHUBRFQRequestDto
@inject IViewLocalizer _localizer
@inject IWebResourceManager WebResourceManager
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment Env

@{
    WebResourceManager.AddScriptModule("/js/modules/orders/sourcing/components/add-edit-sourcing-results-hubrfq.js");
}


<div id="add-edit-sourcing-results-hubrfq-dialog" class="dialog-container d-none" title="@_localizer["Quote to Client"]">
    <script hidden>
        var sourcingResultsString = {
            addSourcingResults: '@_localizer["Add New Sourcing Result"]',
            addSourcingResultsMessage: '@_localizer["Enter the details of the new Sourcing Result and press"]',
            editSourcingResults: '@_localizer["Edit Sourcing Result Details"]',
            editSourcingResultsMessage: '@_localizer["Enter the changed details for the Sourcing Result and press"]',
        };
    </script>
    <div class="dialog-description">
        <div class="d-flex justify-content-between">
            <h5 id="add-edit-sourcing-results-hubrfq-title" class="fw-bold fs-14 text-uppercase">
                @_localizer["Add New Sourcing Result"]
            </h5>
            <span>
                <span class="fw-bold required me-1">*</span>@_commonLocalizer["denotes a required field"]
            </span>
        </div>

        <div class="line"></div>

        <div class="mb-2">
            <span id="add-edit-sourcing-results-hubrfq-message">@_localizer["Enter the details of the new Sourcing Result and press"]</span>
            <b>@_commonLocalizer["Save"]</b>
        </div>

        <div id="add-edit-sourcing-results-hubrfq-form-error-summary" class="form-error-summary" style="display: none;">
            <img src="~/img/icons/x-octagon.svg" alt="Invalid icon" />
            <div>
            </div>
        </div>
    </div>

    <form method="post" id="add-edit-sourcing-results-hubrfq-form" class="row common-form">
        @Html.AntiForgeryToken()

        <input type="number" name="SourcingResultsId" id="SourcingResultsId" hidden aria-label="SourcingResultsId" />

        <div class="col-md-12 form-control-wrapper" id="supplier-container">
            <label for="quote-to-client-supplier-auto-search" class="form-label">@_localizer["Supplier"]<span class="required"> *</span></label>

            <div id="quote-to-client-supplier-auto-search-wrapper" class="position-relative">
                <input type="text" id="quote-to-client-supplier-auto-search" class="form-control form-input"
                       data-search-input="single"
                       data-input-value-id="QuoteToClientSupplierId"
                       data-api-url="/companies/auto-search"
                       data-api-key-search="keyword" aria-label="company-auto-search" />

                <input type="number" id="QuoteToClientSupplierId" name="SupplierNo" hidden aria-label="supplier-id" />
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper d-none" id="label-supplier-container">
            <label for="supplier-auto-search" class="form-label">@_localizer["Supplier"]</label>
            <p id="SupplierName" class="form-p"></p>
        </div>

        <div class="col-md-12 form-control-wrapper d-none" id="location-container">
            <label for="location-dropdown" class="form-label">@_localizer["Location"]<span class="required"> *</span></label>

            <div class="d-flex justify-content-center align-items-center">
                <select class="form-select" aria-label="agencySO" id="location-dropdown" data-bind-name="CountryNo" name="CountryNo">
                    <option selected value="">@_commonLocalizer["Select"]...</option>
                </select>

                <a href="#" class="select-menu-gtv2-refresh-button" id="location-refresh-button">
                    <img alt="refresh-button" src="~/img/icons/refresh-button.png">
                </a>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper d-none" id="type-of-supplier-container">
            <label for="type-of-supplier-dropdown" class="form-label">@_localizer["Type Of Supplier"]</label>

            <div class="d-flex justify-content-center align-items-center">
                <select class="form-select" aria-label="agencySO" id="type-of-supplier-dropdown" data-bind-name="TypeOfSupplier" name="TypeOfSupplier">
                    <option selected value="">@_commonLocalizer["Select"]...</option>
                </select>

                <a href="#" class="select-menu-gtv2-refresh-button" id="type-of-supplier-refresh-button">
                    <img alt="refresh-button" src="~/img/icons/refresh-button.png">
                </a>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper d-none" id="reason-for-chosen-container">
            <label for="reason-for-chosen-dropdown" class="form-label">@_localizer["Reason For Chosen Supplier"]</label>

            <div class="d-flex justify-content-center align-items-center">
                <select class="form-select" aria-label="agencySO" id="reason-for-chosen-dropdown" data-bind-name="ReasonForChosen" name="ReasonForSupplier">
                    <option selected value="">@_commonLocalizer["Select"]...</option>
                </select>

                <a href="#" class="select-menu-gtv2-refresh-button" id="reason-for-chosen-refresh-button">
                    <img alt="refresh-button" src="~/img/icons/refresh-button.png">
                </a>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper d-none" id="risk-of-supplier-container">
            <label for="risk-of-supplier-dropdown" class="form-label">@_localizer["Risk Of Supplier"]</label>

            <div class="d-flex justify-content-center align-items-center">
                <select class="form-select" aria-label="agencySO" id="risk-of-supplier-dropdown" data-bind-name="RiskOfSupplier" name="RiskOfSupplier">
                    <option selected value="">@_commonLocalizer["Select"]...</option>
                </select>

                <a href="#" class="select-menu-gtv2-refresh-button" id="risk-of-supplier-refresh-button">
                    <img alt="refresh-button" src="~/img/icons/refresh-button.png">
                </a>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper" id="part-no-container">
            <label for="sourcing-results-part-no-auto-search" class="form-label">@_localizer["Part No"]<span class="required"> *</span></label>

            <div id="sourcing-results-part-no-auto-search-wrapper" class="position-relative">
                <input type="text" class="form-control form-input" id="SourcingResultsPartNo" name="Part">
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="rohs-dropdown" class="form-label">@_localizer["RoHS"]</label>

            <div class="d-flex justify-content-center align-items-center">
                <select class="form-select" aria-label="agencySO" id="rohs-dropdown" data-bind-name="ROHS" name="ROHS">
                    <option selected value="">@_commonLocalizer["Select"]...</option>
                </select>

                <a href="#" class="select-menu-gtv2-refresh-button" id="Rohs-refresh-button">
                    <img alt="refresh-button" src="~/img/icons/refresh-button.png">
                </a>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper" id="manufacturer-container">
            <label for="quote-to-client-manufacturer-auto-search" class="form-label">@_localizer["Manufacturer"]<span class="required"> *</span></label>

            <div id="quote-to-client-manufacturer-auto-search-wrapper" class="position-relative">
                <input type="text" id="quote-to-client-manufacturer-auto-search" class="form-control form-input"
                       data-search-input="single"
                       data-input-value-id="QuoteToClientManufacturerId"
                       data-api-url="/manufacturers/auto-search"
                       data-api-key-search="keyword" aria-label="quote-to-client-manufacturer-auto-search" />

                <input type="number" id="QuoteToClientManufacturerId" name="ManufacturerNo" hidden aria-label="manufacturer-id" />
                <span id="manufacturer-text" class="d-none">()</span>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="coo-dropdown" class="form-label">@_localizer["Country Of Origin"]</label>

            <div class="d-flex justify-content-center align-items-center">
                <select class="form-select" aria-label="agencySO" id="coo-dropdown" data-bind-name="IHSCountryOfOriginNo" name="IHSCountryOfOriginNo">
                    <option selected value="">@_commonLocalizer["Select"]...</option>
                </select>

                <div class="mx-auto" id="coo-warning"></div>

                <a href="#" class="select-menu-gtv2-refresh-button" id="coo-refresh-button">
                    <img alt="refresh-button" src="~/img/icons/refresh-button.png">
                </a>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="date-code" class="form-label">@_localizer["Date Code"]</label>
            <input type="text" name="DateCode" id="DateCode" data-auto-upper="true" class="form-control form-textarea height-auto" asp-for="@Model.DateCode" maxlength="5" />
        </div>

        <div class="col-md-12 form-control-wrapper" id="product-container">
            <label for="quote-to-client-product-auto-search" class="form-label">@_localizer["Product"]<span class="required"> *</span></label>

            <div id="quote-to-client-product-auto-search-wrapper" class="position-relative">
                <input type="text" id="quote-to-client-product-auto-search" class="form-control form-input"
                       data-search-input="single"
                       data-input-value-id="QuoteToClientProductId"
                       data-api-url="/products/auto-search"
                       data-api-key-search="keyword" />

                <input type="number" id="QuoteToClientProductId" name="ProductNo" hidden aria-label="product-id" />
                <span id="product-text" class="d-none">()</span>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper" id="package-container">
            <label for="quote-to-client-package-auto-search" class="form-label">@_localizer["Package"]</label>

            <div id="quote-to-client-package-auto-search-wrapper" class="position-relative">
                <input type="text" id="quote-to-client-package-auto-search" class="form-control form-input"
                       data-search-input="single"
                       data-input-value-id="QuoteToClientPackageId"
                       data-api-url="/packages/auto-search"
                       data-api-key-search="keyword" />

                <input type="number" id="QuoteToClientPackageId" name="PackageNo" hidden aria-label="package-id" />
                <span id="package-text" class="d-none">()</span>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="Quantity" class="form-label">@_localizer["Quantity"]<span class="required"> *</span></label>
            <div class="d-flex align-items-center">
                <input type="text" class="form-control form-input" name="Quantity" id="Quantity" data-input-type="numeric" data-input-format="int" data-bind-name="Quantity">
                <div class="text-right-nowrap" id="sourcing-results-quantity"></div>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="OfferStatusNo" class="form-label">@_localizer["Offer Status"]</label>

            <div class="d-flex justify-content-center align-items-center">
                <select class="form-select" aria-label="agencySO" id="offer-status-dropdown" data-bind-name="OfferStatusNo" name="OfferStatusNo">
                    <option selected value="">@_commonLocalizer["Select"]...</option>
                </select>

                <a href="#" class="select-menu-gtv2-refresh-button" id="offer-status-no-refresh-button">
                    <img alt="refresh-button" src="~/img/icons/refresh-button.png">
                </a>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="SPQ" class="form-label">@_localizer["Standard Pack Quantity (SPQ)"]</label>
            <div class="d-flex align-items-center">
                <input type="text" class="form-control form-input" id="SPQ" name="SPQ" data-input-type="numeric" data-input-format="int">
                <div class="text-right-nowrap" id="sourcing-results-spq"></div>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="factory-sealed" class="form-label">@_localizer["Factory Sealed (Y/N)"]</label>
            <div class="d-flex align-items-center">
                <input type="text" class="form-control form-input" id="factory-sealed" name="FactorySealed" maxlength="50">
                <div class="text-right-nowrap" id="sourcing-results-factory-sealed"></div>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="msl-dropdown" class="form-label">@_localizer["MSL"]</label>

            <div class="d-flex justify-content-center align-items-center">
                <select class="form-select" aria-label="MslLevelNo" id="msl-dropdown" data-bind-name="MslLevelNo" name="MslLevelNo">
                    <option selected value="">@_commonLocalizer["Select"]...</option>
                </select>

                <a href="#" class="select-menu-gtv2-refresh-button" id="MslLevelNo-refresh-button">
                    <img alt="refresh-button" src="~/img/icons/refresh-button.png">
                </a>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="total-quantity" class="form-label">@_localizer["Total Quantity of Stock Available"]</label>
            <div class="d-flex align-items-center">
                <input type="text" class="form-control form-input" id="total-quantity" name="SupplierTotalQSA" data-input-type="numeric" data-input-format="int">
                <div class="text-right-nowrap" id="sourcing-results-total-quantity"></div>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="moq" class="form-label">@_localizer["Minimum Order Quantity (MOQ)"]</label>
            <div class="d-flex align-items-center">
                <input type="text" class="form-control form-input" id="moq" name="SupplierMOQ" data-input-type="numeric" data-input-format="int">
                <div class="text-right-nowrap" id="sourcing-results-moq"></div>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="ltb" class="form-label">@_localizer["Last time buy (LTB) - (Y/N)"]</label>
            <div class="d-flex align-items-center">
                <input type="text" class="form-control form-input" id="ltb" name="SupplierLTB" maxlength="30">
                <div class="text-right-nowrap" id="sourcing-results-ltb"></div>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="currency-select" class="form-label">@_localizer["Sell Currency"]<span class="required"> *</span></label>

            <div class="d-flex justify-content-center align-items-center" id="currency-select">
                <select class="form-select" aria-label="agencySO" id="currency-dropdown" data-bind-name="CurrencyNo" name="CurrencyNo">
                    <option selected value="">@_commonLocalizer["Select"]...</option>
                </select>

                <a href="javascript:void(0)" class="select-menu-gtv2-refresh-button" id="CurrencyNo-refresh-button">
                    <img alt="refresh-button" src="~/img/icons/refresh-button.png">
                </a>
            </div>
            <span id="no-currency-message" class="d-none">@_localizer["No Supplier Currency"]</span>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="buy-price" class="form-label">@_localizer["Buy Price"]<span class="required"> *</span></label>
            <div class="d-flex align-items-center">
                <input type="text" class="form-control form-input" id="buy-price" name="Price"
                       data-number-force-positive="true"
                       data-number-force-positive-decimal-place="5"
                       data-number-force-positive-round-decimal-place="true">
                <div class="text-right-nowrap" id="sourcing-results-buy-price"></div>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="uplift-sell-price" class="form-label">@_localizer["Uplift Sell Price"]<span class="required"> *</span></label>
            <div class="d-flex align-items-center">
                <input type="text" class="form-control form-input" id="uplift-sell-price" name="SupPrice"
                       data-number-force-positive="true"
                       data-number-force-positive-decimal-place="5"
                       data-number-force-positive-round-decimal-place="true">
                <div class="text-right-nowrap" id="sourcing-results-uplift-sell-price"></div>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper d-none" id="price-warning-message">
            <label class="form-label text-warning">@_localizer["Uplift Sell Price is Less than Buy Price"]</label>
        </div>

        <div class="col-md-12 form-control-wrapper d-none" id="reason">
            <label for="reason" class="form-label">@_localizer["Reason"]<span class="required"> *</span></label>
            <textarea class="form-control form-textarea" id="reason" maxlength="128" name="SellPriceLessReason"></textarea>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="estimated-ship-cost" class="form-label">@_localizer["Estimated Shipping Cost"]</label>
            <div class="d-flex align-items-center">
                <input type="text" class="form-control form-input" id="estimated-ship-cost" name="EstimatedShippingCostValue" data-number-force-positive="true"
                       data-number-force-positive-decimal-place="5"
                       data-number-force-positive-round-decimal-place="true">
                <div class="text-right-nowrap" id="sourcing-results-estimated-ship-cost"></div>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="lead-time" class="form-label">@_localizer["Lead Time (Weeks)"]</label>
            <div class="d-flex align-items-center">
                <input type="text" class="form-control form-input" id="lead-time" name="LeadTime" maxlength="50">
                <div class="text-right-nowrap" id="sourcing-results-lead-time"></div>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="region-dropdown" class="form-label">@_localizer["Region"]</label>

            <div class="d-flex justify-content-center align-items-center">
                <select class="form-select" aria-label="RegionNo" id="region-dropdown" data-bind-name="RegionNo" name="RegionNo">
                    <option selected value="">@_commonLocalizer["Select"]...</option>
                </select>

                <a href="#" class="select-menu-gtv2-refresh-button" id="region-no-refresh-button">
                    <img alt="refresh-button" src="~/img/icons/refresh-button.png">
                </a>
            </div>
        </div>

        <div class="col-4 form-control-wrapper">
            <label for="delivery-date" class="form-label">@_localizer["Delivery Date"]</label>
            <span class="d-flex gap-1">
                <input class="form-control form-input" type="text" name="DeliveryDate" id="delivery-date" placeholder="@_localizer["DD/MM/YYYY"]" data-input-type="DATE">
            </span>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="rohs-status" class="form-label">@_localizer["RoHS Status (Y/N)"]</label>
            <div class="d-flex align-items-center">
                <input type="text" class="form-control form-input" id="rohs-status" name="ROHSStatus" maxlength="50">
                <div class="text-right-nowrap" id="sourcing-results-rohs-status"></div>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="supplier-warranty" class="form-label">@_localizer["Supplier Warranty"]<span id="warranty-required" class="required d-none"> *</span></label>
            <div class="d-flex align-items-center">
                <input type="text" class="form-control form-input" id="supplier-warranty" name="SupplierWarranty" data-input-type="numeric" data-input-format="int">
                <div class="text-right-nowrap" id="sourcing-results-supplier-warranty">@_localizer["days"]</div>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="notes" class="form-label">@_localizer["Notes"]</label>
            <textarea class="form-control form-textarea" id="notes" name="Notes"></textarea>

        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="testing-recommended-checkbox-input" class="form-label">@_localizer["Testing Recommended"]</label>
            <input class="form-control form-check-input mt-0 p-0" type="checkbox" id="testing-recommended-checkbox-input" name="IsTestingRecommended">
        </div>

    </form>
</div>