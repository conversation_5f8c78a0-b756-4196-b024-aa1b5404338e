﻿using GlobalTrader2.Aggregator.UseCases.Orders.PurchaseOrders.GetPurchaseOrderMainInfo;
using GlobalTrader2.Dto.PurchaseOrder;
using GlobalTrader2.Orders.UserCases.Orders.BOM.CompanyAdvisoryNote.CompanyAdvisoryNote.Queries;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetAllPurchaseOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetClosedPurchaseOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetExpediteNotesHistory;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetOpenPurchaseOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetPoCompanyHistoryList;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetPoListApproval;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.SupplierApprovalTradeReference;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.AdvisoryNote.AdvisoryNotes.Queries;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Commands.ClosePurchaseOrder;
using GlobalTrader2.SharedUI;
using GlobalTrader2.SharedUI.Helper;
using Microsoft.Extensions.Localization;
using System.Globalization;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetEprList;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetEprRejectedLog;

using GlobalTrader2.Orders.UserCases.Orders.BOM.CompanyAdvisoryNote.CompanyAdvisoryNote.Queries;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetSuppplierPoApproval;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetSupplierApprovalPoHistory;
namespace GlobalTrader2.Orders.UI.Areas.Orders.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/orders/purchase-orders/details")]
    public class PurchaseOrderDetailsController : ApiBaseController
    {
        private readonly IMediator _mediator;
        private readonly SecurityManager _securityManager;
        private readonly IStringLocalizer<Misc> _miscLocalizer;
        private readonly IStringLocalizer<Status> _statusLocalizer;

        private readonly SessionManager _sessionManager;

        public PurchaseOrderDetailsController(IMediator mediator, SecurityManager securityManager, SessionManager sessionManager, IStringLocalizer<Misc> miscLocalizer, IStringLocalizer<Status> statusLocalizer)
        {
            _mediator = mediator;
            _securityManager = securityManager;
            _sessionManager = sessionManager;
            _miscLocalizer = miscLocalizer;
            _statusLocalizer = statusLocalizer;
        }

        [HttpGet("{purchaseOrderId}/main-info")]
        public async Task<IActionResult> GetPurchaseOrderMainInfo(int purchaseOrderId)
        {
            if (purchaseOrderId <= 0)
            {
                return BadRequest("Invalid purchase order ID.");
            }
            var query = new GetPurchaseOrderMainInfoQuery
            {
                PurchaseOrderId = purchaseOrderId,
                ClientNo = ClientId,
                ClientCurrencyCode = _sessionManager.ClientCurrencyCode,
                ClientCurrencyID = _sessionManager.ClientCurrencyId.GetValueOrDefault(),
                IsPOHub = IsPOHub

            };
            var response = await _mediator.Send(query);
            if (response.Data != null)
            {
                var currency = Functions.FormatCurrency(response.Data.TotalShipInCostVal, CultureInfo.CurrentCulture, _sessionManager.ClientCurrencyCode, 2, true);
                response.Data.TotalShipInCostText = String.Format("({0} {1})", "Recommended Ship In Cost", currency);
                response.Data.DLUPText = LocalizerHelper.FormatDLUP(response.Data.DLUP, response.Data.UpdatedByName ?? "", _miscLocalizer, CultureInfo.CurrentCulture);
                response.Data.Status = response.Data.StatusNo.HasValue ? _statusLocalizer[((PurchaseOrderStatus)response.Data.StatusNo).ToString()] : "";
            }
            return Ok(response);
        }

        [HttpGet("{purchaseOrderId}/supplier-approval/trade-references")]
        public async Task<IActionResult> GetSupplierApprovalTradeReferences(int purchaseOrderId) {
            var result = await _mediator.Send(new GetSupplierApprovalTradeReferenceQuery(purchaseOrderId,UserId, ClientId));
            return Ok(result);
        }

        [HttpGet("{purchaseOrderId}/supplier-approval/approval-status")]
        public async Task<IActionResult> GetSupplierPoApproval(int purchaseOrderId)
        {
            var result = await _mediator.Send(new GetSupplierPoApprovalQuery(purchaseOrderId, UserId, ClientId, _sessionManager.GetCurrentCulture()));
            return Ok(result);
        }

        [HttpGet("{purchaseOrderId}/supplier-approval/approval-history")]
        public async Task<IActionResult> GetSupplierPoApprovalHistory(int purchaseOrderId) { 
            var result = await _mediator.Send(new GetSupplierApprovalPoHistoryQuery(purchaseOrderId, _sessionManager.GetCurrentCulture()));
            return Ok(result);
        }

        [HttpGet("{purchaseOrderId}/expedite-notes")]
        public async Task<IActionResult> GetExpediteNotes(int purchaseOrderId)
        {
            if (purchaseOrderId <= 0)
            {
                return BadRequest("Invalid purchase order ID.");
            }
            var query = new GetExpediteNotesHistoryQuery(purchaseOrderId);
            var response = await _mediator.Send(query);
            if (response.Data != null)
            {
                foreach (var item in response.Data)
                {
                    item.DateTimeString = Functions.FormatDate(item.DLUP, false, true, false, CultureInfo.CurrentCulture);
                }
            }
            return Ok(response);
        }

        [HttpGet("{purchaseOrderId}/approval-history")]
        public async Task<IActionResult> GetApprovalHistory(int purchaseOrderId)
        {
            if (purchaseOrderId <= 0)
            {
                return BadRequest("Invalid purchase order ID.");
            }
            var query = new GetPoListApprovalQuery(purchaseOrderId);
            var approvalList = await _mediator.Send(query);
            var response = new BaseResponse<IReadOnlyList<ApprovalHistoryForTableDto>>
            {
                Success = approvalList.Success,
                Data = approvalList.Data?.Select(x => new ApprovalHistoryForTableDto
                {
                    ID = x.AuditId,
                    Action = x.Note != null ? _miscLocalizer[x.Note] : String.Empty,
                    DateTimeString = Functions.FormatDate(x.DateAuthorised, false, true, false, CultureInfo.CurrentCulture),
                    ApprovedBy = x.EmployeeName
                }).ToList()
            };
            return Ok(response);

        }

        [HttpGet("{purchaseOrderId}/company-history")]
        public async Task<IActionResult> GetCompanyHistory(int purchaseOrderId)
        {
            if (purchaseOrderId <= 0)
            {
                return BadRequest("Invalid purchase order ID.");
            }
            var query = new GetPoCompanyHistoryListQuery(purchaseOrderId);
            var response = await _mediator.Send(query);
            if (response.Data != null)
            {
                foreach (var item in response.Data)
                {
                    item.DateTimeString = Functions.FormatDate(item.DLUP, false, true, false, CultureInfo.CurrentCulture);
                    var companyNotesQuery = new GetCompanyAdvisoryNoteQuery { Id = item.CompanyNo };
                    var companyNotes = await _mediator.Send(companyNotesQuery);
                    item.CompanyAdvisoryNotes = Functions.ReplaceLineBreaks(companyNotes.Data) ?? string.Empty;
                }
            }
            return Ok(response);
        }

        [HttpGet("{purchaseOrderId}/epr/list")]
        public async Task<IActionResult> GetPurchaseOrderEprList(int purchaseOrderId, [FromQuery] string? orderBy)
        {
            var result = await _mediator.Send(new GetEprListQuery(purchaseOrderId, orderBy));

            return Ok(result);
        }

        [HttpGet("epr/{eprNo}/rejected-log")]
        public async Task<IActionResult> GetPurchaseOrderEprRejectedLog(int eprNo)
        {
            var result = await _mediator.Send(new GetEprRejectedLogQuery(eprNo));

            return Ok(result);
        }

        [HttpGet("{purchaseOrderId}/all-po-lines")]
        public async Task<IActionResult> GetAllPurchaseOrderLineAsync([FromRoute]int purchaseOrderId)
        {
            var result = await _mediator.Send(new GetAllPurchaseOrderLineQuery()
            {
                PurchaseOrderId = purchaseOrderId
            });
            var respone = new BaseResponse<LineListDto>();
            
            if (result.Success && result.Data != null)
            {
                respone.Success = true;
                respone.Data = await ProcessLineList(result.Data.ToList());
            }
            
            return Ok(respone);
        }

        [HttpGet("{purchaseOrderId}/open-po-lines")]
        public async Task<IActionResult> GetOpenPurchaseOrderLineAsync([FromRoute] int purchaseOrderId)
        {
            var result = await _mediator.Send(new GetOpenPurchaseOrderLineQuery()
            {
                PurchaseOrderId = purchaseOrderId
            });
            var respone = new BaseResponse<LineListDto>();

            if (result.Success && result.Data != null)
            {
                respone.Success = true;
                respone.Data = await ProcessLineList(result.Data.ToList());
            }

            return Ok(respone);
        }

        [HttpGet("{purchaseOrderId}/closed-po-lines")]
        public async Task<IActionResult> GetClosedPurchaseOrderLineAsync([FromRoute] int purchaseOrderId)
        {
            var result = await _mediator.Send(new GetClosedPurchaseOrderLineQuery()
            {
                PurchaseOrderId = purchaseOrderId
            });
            var respone = new BaseResponse<LineListDto>();

            if (result.Success && result.Data != null)
            {
                respone.Success = true;
                respone.Data = await ProcessLineList(result.Data.ToList());
            }

            return Ok(respone);
        }

        private async Task<LineListDto> ProcessLineList(List<PurchaseOrderLineDetailDto> poLineDetailList)
        {
            var lineList = new LineListDto();
            var lineDetailList = new List<PurchaseOrderLineDetailInfoDto>();
            decimal dblSubTotal = 0;
            decimal dblTax = 0;
            string strCurrency = "";
            var listMfrNotes = await _mediator.Send(new GetAdvisoryNotesQuery()
            {
                Ids = string.Join(",", poLineDetailList.Select(x => x.ManufacturerNo).ToList()),
                ClientId = ClientId,
            });
            foreach (var item in poLineDetailList)
            {
                strCurrency = item.CurrencyCode;
                var dblLineTotal = item.Price * item.Quantity;
                var dblLineTax = item.Taxable ? (dblLineTotal * (((item.TaxRate == null) ? 0 : item.TaxRate) / 100)) : 0;
                if (item.Posted)
                {
                    dblSubTotal += dblLineTotal;
                    dblTax += dblLineTax.GetValueOrDefault();
                }

                string mfrNotes = listMfrNotes.Data?.FirstOrDefault(x => x.ManufacturerId == item.ManufacturerNo.GetValueOrDefault())?.AdvisoryNotes ?? string.Empty;
                var lineItem = new PurchaseOrderLineDetailInfoDto
                {
                    IsIPO = item.InternalPurchaseOrderId > 0,
                    Closed = item.Closed,
                    Posted = item.Posted,
                    Taxable = item.Taxable,
                    IsPosted = item.Posted,
                    IsAllocated = item.QuantityAllocated == item.Quantity,
                    IsReceived = item.QuantityReceived > 0,
                    Inactive = item.Inactive,
                    IsReleased = item.IsReleased ?? false,
                    IsAuthorised = item.IsAuthorised ?? false,
                    //IsQualityUser = PurchaseOrderLine.IsQualityGroupUser(SessionManager.LoginID, SessionManager.ClientID),
                  
                    Part = item.Part,
                    ROHS = item.ROHS,
                    SupplierPart = item.SupplierPart,
                    DateCode = item.DateCode,
                    Mfr = item.ManufacturerCode,
                    MfrNo = item.ManufacturerNo,
                    MfrAdvisoryNotes = Functions.ReplaceLineBreaks(mfrNotes),
                    Product = item.ProductDescription,
                    Package = item.PackageName,
                    CustomerNo = item.CompanyNo,
                    Customer = item.CompanyName,
                    InternalPurchaseOrderNumber = item.InternalPurchaseOrderNumber,
                    SupplierApprovalStatus = item.SupplierApprovalStatus,
                    SupplierApprovalStatusMessage = item.SupplierApprovalStatusMessage,
                    AS6081 = item.AS6081,
                    ReceivingNotes = item.ReceivingNotes,

                    LineID = item.PurchaseOrderLineId,
                    LineNo = item.POSerialNo ?? 0,
                    PONo = item.PurchaseOrderNumber,

                    QuantityOrdered = item.Quantity,
                    QuantityAllocated = item.QuantityAllocated,
                    QuantityReceived = item.QuantityReceived,
                    QuantityOutstanding = Math.Max(item.Quantity - item.QuantityReceived, 0),

                    Price = Functions.FormatCurrency(item.Price, CultureInfo.CurrentCulture, item.CurrencyCode, 5, true),
                    ShipInCost = Functions.FormatCurrency(item.ShipInCost ?? 0, CultureInfo.CurrentCulture, _sessionManager.ClientCurrencyCode, 5, false),
                    LineTotal = Functions.FormatCurrency(item.Price * item.Quantity, CultureInfo.CurrentCulture, item.CurrencyCode, 2, true),
                    Tax = Functions.FormatCurrency(dblLineTax, CultureInfo.CurrentCulture, item.CurrencyCode, 2, true),

                    PriceVal = Functions.FormatCurrency(item.Price, CultureInfo.CurrentCulture, null, 5, true),
                    ShipInCostVal = Functions.FormatCurrency(item.ShipInCost ?? 0, CultureInfo.CurrentCulture, null, 5, false),
                    SellingPrice = Functions.FormatCurrency(item.Price, CultureInfo.CurrentCulture, item.CurrencyCode, 5, false),

                    DateOrdered = item.DateOrdered,
                    DeliveryDate = item.DeliveryDate,
                    RequiredDate = item.PromiseDate,
                };

                lineDetailList.Add(lineItem);
            }
            lineList.Count = poLineDetailList.Count;
            lineList.Lines = lineDetailList;
            lineList.Tax = Functions.FormatCurrency(dblTax, CultureInfo.CurrentCulture, strCurrency, 2, true);
            lineList.SubTotal = Functions.FormatCurrency(dblSubTotal, CultureInfo.CurrentCulture, strCurrency, 2, true);
            lineList.Total = Functions.FormatCurrency(dblSubTotal + dblTax, CultureInfo.CurrentCulture, strCurrency, 2, true);
            return lineList;
        }

        [HttpPut("{purchaseOrderId}/close")]
        public async Task<IActionResult> ClosePurchaseOrder(int purchaseOrderId)
        {
            if (purchaseOrderId <= 0)
            {
                return BadRequest("Invalid purchase order ID.");
            }
            var updatedBy = UserId;
            var command = new ClosePurchaseOrderCommand(purchaseOrderId, updatedBy);
            var response = await _mediator.Send(command);
            return Ok(response);

        }
    }
}
