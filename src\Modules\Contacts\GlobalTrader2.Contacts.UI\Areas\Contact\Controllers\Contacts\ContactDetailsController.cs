﻿using AutoMapper;
using GlobalTrader2.Contacts.UseCases.Contact.ContactDetails.Commands.InsertContact;
using GlobalTrader2.Contacts.UseCases.Contact.ContactDetails.Commands.InsertContactLog;
using GlobalTrader2.Contacts.UseCases.Contact.ContactDetails.Commands.Update;
using GlobalTrader2.Contacts.UseCases.Contact.ContactDetails.Commands.UpdateContact;
using GlobalTrader2.Contacts.UseCases.Contact.ContactDetails.Commands.UpdateContactLog;
using GlobalTrader2.Contacts.UseCases.Contact.ContactDetails.Queries.GetContact;
using GlobalTrader2.Contacts.UseCases.Contact.ContactDetails.Queries.GetContactDetails;
using GlobalTrader2.Contacts.UseCases.Contact.ContactDetails.Queries.GetContactList;
using GlobalTrader2.Core.Bases;
using GlobalTrader2.Dto.CommunicationLog;
using GlobalTrader2.Dto.Contact;
using GlobalTrader2.Dto.Contacts;
using GlobalTrader2.SharedUI;
using GlobalTrader2.SharedUI.Authorization;
using GlobalTrader2.SharedUI.Bases;
using GlobalTrader2.SharedUI.Enums;
using GlobalTrader2.SharedUI.Helper;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using System.Globalization;

namespace GlobalTrader2.Contacts.UI.Areas.Contact.Controllers.Contacts
{
    [ApiAuthorize(isDataOtherClient: false, permissions: SecurityFunction.Contact_ContactDetail_View)]
    [ApiController]
    [Route("api/contact/contacts")]
    public class ContactDetailsController : ApiBaseController
    {
        private readonly ISender _mediator;
        private readonly IMapper _mapper;
        private readonly IStringLocalizer<Misc> _miscLocalizer;

        public ContactDetailsController(ISender mediator, IMapper mapper, IStringLocalizer<Misc> miscLocalizer)
        {
            _mediator = mediator;
            _mapper = mapper;
            _miscLocalizer = miscLocalizer;
        }

        [HttpGet("list")]
        public async Task<IActionResult> GetContactListByCompany(int CompanyId)
        {
            var result = await _mediator.Send(new GetContactListQuery(CompanyId));
            return Ok(result);
        }
        [HttpGet("{id}")]
        public async Task<IActionResult> GetContact(int id)
        {
            var response = new BaseResponse<ContactDetailsResponse>();
            var contactDetailsDtoResponse = await _mediator.Send(new GetContactDetailsQuery(id));

            response.Success = true;
            response.Data = _mapper.Map<ContactDetailsResponse>(contactDetailsDtoResponse.Data);
            response.Data.DLUP = LocalizerHelper.FormatDLUP(contactDetailsDtoResponse.Data?.DLUP, contactDetailsDtoResponse.Data?.UpdatedByName, _miscLocalizer, CultureInfo.CurrentCulture);

            return Ok(response);
        }

        [HttpPut("{contactId}")]
        [ValidateAntiForgeryToken]
        [ApiAuthorize(false, SecurityFunction.Contact_ContactDetail_MainInfo_Edit)]
        public async Task<IActionResult> UpdateContact([FromBody] UpdateContactRequestDto request, int contactId)
        {
            var contact = await _mediator.Send(new GetContactQuery(contactId));
            if (contact.Data == null) return BadRequest();

            request.ContactId = contactId;
            request.UpdatedBy = UserId;
            request.ContactName = contact.Data.ContactName;
            request.CompanyNo = contact.Data.CompanyNo;
            request.Notes = contact.Data.Notes;

            var command = new UpdateContactCommand(request);
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        [HttpPut("defaultPO")]
        [ValidateAntiForgeryToken]
        [ApiAuthorize(false, SecurityFunction.Contact_ContactDetail_ContactList_MakePODefault)]
        public async Task<IActionResult> UpdateDefaultPO([FromBody] MakeDefaultForContactDto request)
        {
            var command = new UpdateDefaultSpoCommand(
                CompanyId: request.CompanyId,
                DefaultSOContactNo: null,
                DefaultPOContactNo: request.ContactId,
                DefaultSOLedgerContactNo: null,
                DefaultPOLedgerContactNo: null,
                UpdatedBy: UserId
            );
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        [HttpPut("defaultSO")]
        [ValidateAntiForgeryToken]
        [ApiAuthorize(false, SecurityFunction.Contact_ContactDetail_ContactList_MakeSODefault)]
        public async Task<IActionResult> UpdateDefaultSO([FromBody] MakeDefaultForContactDto request)
        {
            var command = new UpdateDefaultSpoCommand(
                CompanyId: request.CompanyId,
                DefaultSOContactNo: request.ContactId,
                DefaultPOContactNo: null,
                DefaultSOLedgerContactNo: null,
                DefaultPOLedgerContactNo: null,
                UpdatedBy: UserId
            );
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        [HttpPut("defaultSOLedger")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateDefaultSOLedger([FromBody] MakeDefaultForContactDto request)
        {
            var command = new UpdateDefaultSpoCommand(
                CompanyId: request.CompanyId,
                DefaultSOContactNo: null,
                DefaultPOContactNo: null,
                DefaultSOLedgerContactNo: request.ContactId,
                DefaultPOLedgerContactNo: null,
                UpdatedBy: UserId
            );
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        [HttpPut("defaultPOLedger")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateDefaultPOLedger([FromBody] MakeDefaultForContactDto request)
        {
            var command = new UpdateDefaultSpoCommand(
                CompanyId: request.CompanyId,
                DefaultSOContactNo: null,
                DefaultPOContactNo: null,
                DefaultSOLedgerContactNo: null,
                DefaultPOLedgerContactNo: request.ContactId,
                UpdatedBy: UserId
            );
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        [HttpDelete("{contactId}")]
        [ApiAuthorize(false, SecurityFunction.Contact_ContactDetail_ContactList_Delete)]
        public async Task<IActionResult> MakeInactiveContact([FromRoute] int contactId)
        {
            var contact = await _mediator.Send(new GetContactQuery(contactId));
            var command = new UpdateContactCommand(
                Data: new UpdateContactRequestDto
                {
                    ContactId = contactId,
                    Inactive = true,
                    UpdatedBy = UserId,
                    ContactName = contact.Data!.ContactName,
                    Salutation = contact.Data.Salutation,
                    FirstName = contact.Data.FirstName,
                    LastName = contact.Data.LastName,
                    Telephone = contact.Data.Telephone,
                    Extension = contact.Data.Extension,
                    Fax = contact.Data.Fax,
                    Title = contact.Data.Title,
                    Email = contact.Data.Email,
                    HomeTelephone = contact.Data.HomeTelephone,
                    MobileTelephone = contact.Data.MobileTelephone,
                    AddressNo = contact.Data.AddressNo,
                    TextOnlyEmail = contact.Data.TextOnlyEmail,
                    FinanceContact = contact.Data.FinanceContact,
                    IsSendShipNotify = contact.Data.IsSendShipNotify,
                    CompanyNo = contact.Data.CompanyNo,
                    Notes = contact.Data.Notes,
                }
            );

            var result = await _mediator.Send(command);
            return Ok(result);
        }

        [ApiAuthorize(false, SecurityFunction.Contact_ContactDetail_ContactList_Add)]
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> InsertContact([FromBody] InsertContactRequest request)
        {
            var command = _mapper.Map<InsertContactCommand>(request);
            command.UpdatedBy = UserId;
            var result = await _mediator.Send(command);
            return Ok(result);
        }

        [ApiAuthorize(false, SecurityFunction.Contact_ContactDetail_ContactLog_Add)]
        [HttpPost("contact-log")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> InsertContactLog([FromBody] CreateCommunicationLogRequestDto request)
        {
            request.UpdatedBy = UserId;
            request.Frozen = false;
            var result = await _mediator.Send(new InsertContactLogCommand(request));
            return Ok(result);
        }

        [ApiAuthorize(false, SecurityFunction.Contact_ContactDetail_ContactLog_Edit)]
        [HttpPut("contact-log/{contactLogId}")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateContactLog([FromBody] UpdateCommunicationRequestDto request, int contactLogId)
        {
            request.CommunicationLogId = contactLogId;
            request.UpdatedBy = UserId;
            var result = await _mediator.Send(new UpdateContactLogCommand(request));
            return Ok(result);
        }
    }
}
