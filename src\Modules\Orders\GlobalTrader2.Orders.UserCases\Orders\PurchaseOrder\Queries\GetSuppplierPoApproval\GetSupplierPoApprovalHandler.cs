﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Dto.PurchaseOrder;
using GlobalTrader2.Orders.UserCases.Orders.BOM.CompanyAdvisoryNote.CompanyAdvisoryNote.Queries;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetIsQualityGroupUser;

namespace GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetSuppplierPoApproval
{
    public class GetSupplierPoApprovalHandler : IRequestHandler<GetSupplierPoApprovalQuery, BaseResponse<GetApprovalStatusDataResponse<SupplierPoApprovalDto>>>
    {
        private readonly IBaseRepository<SupplierPoApprovalReadModel> _repository;
        private readonly IMapper _mapper;
        private readonly IMediator _mediator;
        public GetSupplierPoApprovalHandler(IBaseRepository<SupplierPoApprovalReadModel> repository, IMapper mapper, IMediator mediator)
        {
            _repository = repository;
            _mapper = mapper;
            _mediator = mediator;
        }

        public async Task<BaseResponse<GetApprovalStatusDataResponse<SupplierPoApprovalDto>>> Handle(GetSupplierPoApprovalQuery request, CancellationToken cancellationToken)
        {
            var readModels = await _repository.SqlQueryRawAsync(
                sql: $"{StoredProcedures.Select_SupplierPoApproval} @PurchaseOrderNo, @LoginId",
                parameters: [
                    new SqlParameter("@PurchaseOrderNo", SqlDbType.Int) { Value = request.PurchaseOrderNo },
                    new SqlParameter("@LoginId", SqlDbType.Int) { Value = request.LoginId }
                ]
            );
            var dtos = await ProcessSupplierApprovalList(readModels.ToList(), request);
            return new BaseResponse<GetApprovalStatusDataResponse<SupplierPoApprovalDto>>
            {
                Success = true,
                Data = new GetApprovalStatusDataResponse<SupplierPoApprovalDto>()
                {
                    Count = dtos.Count(),
                    Lines = dtos.ToList()
                }
            };
        }

        private async Task<IEnumerable<SupplierPoApprovalDto>> ProcessSupplierApprovalList(List<SupplierPoApprovalReadModel> readModels, GetSupplierPoApprovalQuery request)
        {
            List<SupplierPoApprovalDto> dtos = [];
            if (readModels.Count == 0)
                return [];

            foreach (var readModel in readModels)
            {
                var dto = _mapper.Map<SupplierPoApprovalDto>(readModel);
                dto.IsQualityUser = await IsQualityGroupUser(request.LoginId, request.ClientId);
                dto.SupplierAdvisoryNotes = await GetSupplierNotes(dto.SupplierId);
                dto.WarrantyPeriod = readModel.WarrantyPeriod != -1 ? $"{readModel.WarrantyPeriod} days" : "";
                dto.QualityApproveDate = $"({Functions.FormatDate(readModel.QualityDLUP, false, true, request.CultureInfo)})";
                dto.LineManagerApproveDate = readModel.IsSendToQuality == true ? "" : $"({Functions.FormatDate(readModel.LineManagerDLUP, false, true, request.CultureInfo)})";
                dtos.Add(dto);
            }
            return dtos;
        }

        private async Task<string> GetSupplierNotes(int supplierId)
        {
            if (supplierId < 0) return string.Empty;
            var advisoryNote = await _mediator.Send(new GetCompanyAdvisoryNoteQuery() { Id = supplierId });
            return advisoryNote.Data ?? string.Empty;
        }

        private async Task<bool> IsQualityGroupUser(int userId, int clientId)
        {
            var isQualityGroupUser = await _mediator.Send(new GetIsQualityGroupUserQuery(userId, clientId));
            return isQualityGroupUser.Data;
        }
    }
}
