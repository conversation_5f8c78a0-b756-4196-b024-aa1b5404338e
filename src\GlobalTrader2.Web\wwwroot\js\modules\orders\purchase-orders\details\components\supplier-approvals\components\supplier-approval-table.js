import { SupplierApprovalTableConfiguration } from "../config/supplier-approval-table-configuration.js?v=#{BuildVersion}#";
import { ResizeLiteDatatable } from "../../../../../../../components/base/resize-lite-datatable/resize-lite-datatable.component.js?v=#{BuildVersion}#";
import { ResizeDatatableEvents } from "../../../../../../../components/base/resize-lite-datatable/resize-lite-datatable-events.constanst.js?v=#{BuildVersion}#";   

export class SupplierApprovalTableManager extends ResizeLiteDatatable {
    constructor(tableId, data, tabLevel) {
        super(
            tableId,
            '',
            {
                resizeConfig: {
                    numberOfRowToShow: 3
                },
                ajax: null,
                data: data,
                columns: SupplierApprovalTableConfiguration[tabLevel].columns,
            }
        )
    }
    setUpTableEventListeners() {
        this.datatable.on('select deselect', (e, dt, type, indexes) => {
            if (type !== 'row') return;
            const selectedRows = dt.rows({ selected: true }).data().toArray();
            this.triggerAsync(ResizeDatatableEvents.SELECT_DESELECT, selectedRows);
        });
    }
}