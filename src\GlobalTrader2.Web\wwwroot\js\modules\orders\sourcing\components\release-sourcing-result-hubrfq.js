﻿
$(() => {
    const yes = window.localizedStrings.yes;
    const no = window.localizedStrings.no;
    const saveChangedMessage = window.localizedStrings.saveChangedMessage;
    const unexpectedErrorMessage = window.localizedStrings.unexpectedError;
    let customerRequirementId;
    const deleteSourcingDialog = $("#release-sourcing-result-dialog");
    deleteSourcingDialog.dialog();
    const header = { "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() };

    let isSubmitting = false;
    let deleteSourcingResultDialog = deleteSourcingDialog.dialog({
        width: 350,
        height: "auto",
        autoOpen: false,
        draggable: false,
        modal: true,
        buttons: {
            "Yes": {
                text: yes,
                class: 'btn btn-primary',
                html: `<img src="/img/icons/check.svg" alt="Yes icon"/>${yes}`,
                click: async function () {
                    if (isSubmitting)
                        return;
                    isSubmitting = true;
                    try {
                        let sourcingId = $('#sourcing-result-id').val();
                        deleteSourcingResult(sourcingId);
                    }
                    catch (error) {
                        showToast('danger', unexpectedErrorMessage);
                    }
                    isSubmitting = false;
                }
            },
            "No": {
                text: no,
                class: 'btn btn-danger',
                html: `<img src="/img/icons/xmark.svg" alt="No icon"/>${no}`,
                click: () => {
                    deleteSourcingResultDialog.dialog("close");
                }
            },
        }
    });

    async function deleteSourcingResult(sourcingId) {
        if (!sourcingId) return;

        const currentDialog = $("#release-sourcing-result-dialog").dialog("instance");
        currentDialog.setLoading(true);
        $("#invalid-price-box").hide();

        const request = {
            sourcingResultID: sourcingId,
            bomId: stateValue.id,
            bOMName: stateValue.data.name,
            salesManNo: stateValue.data.requestToPOHubBy,
            hUBRFQStatus: $('#bom-details-status').text(),
            bomCompanyNo: stateValue.data.companyNo,
            bOMCode: stateValue.data.code,
            reqSalesPerson: stateValue.data.reqSalesPerson,
            supportTeamMemberNo: stateValue.data.supportTeamMemberNo,
            requirementId: stateValue.selectedRequirementDetails.id
        };
        customerRequirementId = $("#requirement-id").val();
        let response = await GlobalTrader.ApiClient.putAsync(`orders/bom/release-sourcing-result`, request, header);
        if (!response.success) {
            currentDialog.setLoading(false);
            $("#invalid-price-box").show();
            showToast("danger", unexpectedErrorMessage);
        }
        else {
            currentDialog.setLoading(false);
            $("#invalid-price-box").show();
            deleteSourcingResultDialog.dialog("close");
            showToast('success', saveChangedMessage);
            $('#sourcing-result-detail').addClass("d-none");
            (async () => {
                if (window.sourcingResultsSectionBox) {
                    await window.sourcingResultsSectionBox.loadSourcingResultsAsync(customerRequirementId);
                    await window.sourcingResultsSectionBox.reSetupAfterLoadData();
                }
            })();
        }
    }
})
