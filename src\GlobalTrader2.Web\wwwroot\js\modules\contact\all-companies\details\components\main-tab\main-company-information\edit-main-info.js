﻿import { SearchSelectComponent } from '../../../../../../../components/search-select/search-select.component.js?v=#{BuildVersion}#';

export class EditCompanyMainInfoManager {
    constructor({
        companyId,
        successCallback
    }) {
        this.isFirstOpen = true;
        this.companyId = companyId;
        this.successCallback = successCallback;
        this.companyMainInfo = null;

        this.$dialog = $("#edit-main-info-dialog");
        this.$form = $("#edit-main-info-form");
        this.$industryType = this.$form.find("#industry-type-dual-listbox");
        this.$salesmanDropdown = this.$form.find("#Salesman");
        this.$typeNoDropdown = this.$form.find("#TypeNo");
        this.$parentCompanySearch = this.$form.find("#ParentCompanySearch");
        this.parentCompanySearch = null;
        this.$reviewDate = this.$form.find("#ReviewDate");
        this.$groupCodeNoDropdown = this.$form.find("#GroupCodeNo");
        this.$lastReviewDate = this.$form.find("#LastReviewDate");
        this.$previousReviewDate = this.$form.find("#PreviousReviewDate");
        this.$isPremierCustomer = this.$form.find("#IsPremierCustomer");
        this.$isTier2PremierCustomer = this.$form.find("#IsTier2PremierCustomer");
        this.$groupCodeNoWrapper = this.$form.find("#groupCodeNoDiv");
        this.$lastReviewDateWrapper = this.$form.find("#last-review-date-wrapper");
        this.$salesmanRequire = this.$form.find("#salesmanReq");
        this.$isCustomer = this.$form.find("#IsCustomer");
        this.$upLiftPrice = this.$form.find("#upLiftPrice");
        this.$supplierWarranty = this.$form.find("#SupplierWarranty");
    }

    initialize() {
        this.setupDialog();
        this.setUpDualListBox();
        this.setupParentCompanySearch();
        this.setupDatePicker();
        this.setupCheckBox();
        this.setupForm();
    }

    openDialog() {
        this.$dialog.dialog("open");
        this.handleDialogOpen();
    }

    setupDialog() {
        this.$dialog.dialog({
            maxHeight: $(window).height(),
            width: "60vw",
            close: function () {
                $(this).find(".form-error-summary").hide();
                $(this).find('.is-invalid').removeClass("is-invalid");
                $(this).find('#Salesman-error').remove();
            },
            buttons: [
                {
                    text: window.localizedStrings.save,
                    class: 'btn btn-primary fw-normal',
                    html: `<img src="/img/icons/save.svg" alt="${window.localizedStrings.save}">${window.localizedStrings.save}`,
                    click: async () => {
                        if (this.$form.valid())
                        {
                            this.$dialog.find(".form-error-summary").hide();
                            this.$dialog.parent().find(`.ui-dialog-buttonset button:has(img[alt="${window.localizedStrings.save}"])`).prop('disabled', true);
                            this.$dialog.dialog("setLoading", true);

                            const response = await this.editCompanyMainInfo();

                            this.$dialog.dialog("setLoading", false);
                            this.$dialog.parent().find(`.ui-dialog-buttonset button:has(img[alt="${window.localizedStrings.save}"])`).prop('disabled', false);

                            if (!response?.success) {
                                showToast("danger", response.title);
                                return;
                            }
                            this.$dialog.dialog("close");
                            showToast('success', window.localizedStrings.saveChangedMessage);

                            if (this.successCallback) this.successCallback();
                        }
                        else
                        {
                            this.$dialog.find(".form-error-summary").show();
                        }
                    }
                },
                {
                    text: window.localizedStrings.cancel,
                    class: 'btn btn-danger fw-normal',
                    html: `<img src="/img/icons/slash.svg" alt="${window.localizedStrings.cancel}">${window.localizedStrings.cancel}`,
                    click: function () {
                        $(this).dialog("close");
                    },
                },
            ]
        });
    }

    async handleDialogOpen() {
        this.$dialog.dialog("setLoading", true);

        if (this.isFirstOpen) {
            this.setupDropDown();
            this.isFirstOpen = false;
        }

        await this.getMainCompanyInfo()

        await this.$industryType.dual_listbox("loadDataAndRenderItem");

        this.loadDataToForm();

        this.$dialog.dialog("setLoading", false);

        $(this.$dialog)[0].scrollTop = 0;
        this.$form.find('input:visible:first').trigger('focus');
    }

    setupDropDown() {

        this.$salesmanDropdown.dropdown({
            serverside: false,
            endpoint: '/lists/employee',
            valueKey: 'loginId',
            textKey: 'employeeName',
            placeHolderValue: "",
        });

        this.$typeNoDropdown.dropdown({
            serverside: false,
            endpoint: '/lists/company-types',
            valueKey: 'companyTypeId',
            textKey: 'name',
            placeHolderValue: "",
        });

        this.$groupCodeNoDropdown.dropdown({
            serverside: false,
            endpoint: '/lists/contact-groups?type=customer&isForDropdown=true',
            valueKey: 'itemId',
            textKey: 'contactName',
            placeHolderValue: "",
        });
    }

    setUpDualListBox() {
        this.$industryType.dual_listbox({
            isLoadDataOnCreate: false,
            loadSelected: async (element) => {
                const selectedIndustryTypes = this.companyMainInfo.industryTypes.filter(x => x.selected);

                return selectedIndustryTypes.map(type => ({
                    label: type.name,
                    value: type.id
                })) ?? [];
            },
            loadUnselected: async (element) => {
                const unSelectedIndustryTypes = this.companyMainInfo.industryTypes.filter(x => !x.selected);

                return unSelectedIndustryTypes.map(type => ({
                    label: type.name,
                    value: type.id
                })) ?? [];
            },
            processLoadedData: function (selected, unSelected) {
                return { selected, unSelected }
            },
        });
    }

    setupCheckBox() {
        this.$isPremierCustomer.on("change", () => {
            this.updateGroupCodeDropdownState();

            if (this.$isPremierCustomer.prop("checked")) {
                this.$isTier2PremierCustomer.prop("checked", false);
            }
        })

        this.$isTier2PremierCustomer.on("change", () => {
            this.updateGroupCodeDropdownState();

            if (this.$isTier2PremierCustomer.prop("checked")) {
                this.$isPremierCustomer.prop("checked", false);
            }
        })

        this.$isCustomer.on("change", () => {
            this.updateSalePersonDropdownState();
        })
    }

    async getMainCompanyInfo() {
        let response = await GlobalTrader.ApiClient.getAsync(`/contact/all-companies/${this.companyId}/main-info`);
        if (response.success) {
            this.companyMainInfo = response.data;

        } else {
            console.log(response.errors);
        }
    }

    setupParentCompanySearch() {
        this.parentCompanySearch = new SearchSelectComponent(
            "ParentCompanySearch",
            "ParentCompanyNo",
            "single",
            "keyword",
            "/companies/auto-search",
        );
    }

    setupDatePicker() {
       $('#ReviewDate').datepicker2({
            showButtonPanel: true,
            dateFormat: "dd/mm/yy",       
            changeMonth: true,
            changeYear: true,
            showAnim: "slideDown",
            showOn: "both",
            buttonImage: "/img/icons/calendar.svg",
            buttonImageOnly: true,
        });
    }

    loadDataToForm() {
        this.$form.find("select").each((i, element) => {
            const value = this.companyMainInfo[this.lowerCaseFirst(element.name)];
            $(element).dropdown("select", value);
        })

        this.$form.find("textarea").each((i, element) => {
            const value = this.companyMainInfo[this.lowerCaseFirst(element.name)];
            $(element).val(value);
        })

        this.$form.find("input[type=text]").each((i, element) => {
            const value = this.companyMainInfo[this.lowerCaseFirst(element.name)];
            $(element).val(value);
        })

        this.$form.find("p[data-field]").each((i, element) => {
            const fieldName = $(element).data("field");
            const value = this.companyMainInfo[this.lowerCaseFirst(fieldName)];
            $(element).html(value);
        })

        this.$form.find("input[type=checkbox].form-check-input").each((i, element) => {
            $(element).prop("checked", this.getPropertyCaseInsensitive(this.companyMainInfo, element.name)).trigger("change");
        })

        if (this.companyMainInfo.parentCompanyName && this.companyMainInfo.parentCompanyNo) {
            this.parentCompanySearch.selectItem({
                label: this.companyMainInfo.parentCompanyName,
                value: this.companyMainInfo.parentCompanyNo,
            })
        }

        if (this.companyMainInfo.reviewDate) {
            this.$reviewDate.datepicker("setDate", this.companyMainInfo.reviewDateText);
        }

        this.$lastReviewDate.text(this.companyMainInfo.lastReviewDateText);
        this.$previousReviewDate.text(this.companyMainInfo.previousReviewDateText);

        if (this.companyMainInfo.lastReviewDateText) {
            this.$lastReviewDateWrapper.show();
        } else {
            this.$lastReviewDateWrapper.hide();
        }
    }

    lowerCaseFirst(str) {
        if (!str) return '';
        return str.charAt(0).toLowerCase() + str.slice(1);
    }

    getPropertyCaseInsensitive(obj, key) {
        key = key.toLowerCase();
        for (let prop in obj) {
            if (prop.toLowerCase() === key) {
                return obj[prop];
            }
        }
        return undefined; // not found
    }

    updateGroupCodeDropdownState() {
        if (this.$isPremierCustomer.prop("checked") || this.$isTier2PremierCustomer.prop("checked")){
            this.$groupCodeNoWrapper.show();
        } else {
            this.$groupCodeNoWrapper.hide();
        }
    }

    updateSalePersonDropdownState() {
        if (this.$isCustomer.prop("checked")) {
            this.$salesmanRequire.show();
        } else {
            this.$salesmanRequire.hide();
        }
    }

    setupForm() {
        this.$form.validate({
            rules: {
                CompanyName: {
                    required: true,
                    maxlength: 128
                },
                url: {
                    required: true,
                    maxlength: 128,
                },
                Salesman: {
                    min: {
                        param: 1,
                        depends: () => {
                            return this.$isCustomer.prop("checked");
                        }
                    }
                },
            },
            messages: {
                CompanyName: window.localizedStrings.requiredField,
                url: window.localizedStrings.requiredField,
                Salesman: window.localizedStrings.requiredField,
            },
            // Focus the first invalid element
            invalidHandler: function (event, validator) {
                if (validator.errorList.length) {
                    $(validator.errorList[0].element).trigger("focus");
                }
            },
        });

        this.$upLiftPrice.on('input', (event) => {
            const value = $(event.target).val()?.trim();
            const numericValue = value.replace(/\D/g, '').slice(0, 10)
            $(event.target).val(numericValue);
        })

        this.$supplierWarranty.on('input', (event) => {
            const value = $(event.target).val()?.trim();
            const numericValue = value.replace(/\D/g, '').slice(0, 10)
            $(event.target).val(numericValue);
        })
    }

    editCompanyMainInfo() {
        const { changedSelecteds, changedUnselecteds } = this.$industryType.dual_listbox("getData");

        const formData = GlobalTrader.FormHelper.convertFormDataToOject(this.$form);

        formData.GroupCodeNo = formData.GroupCodeNo == "0" ? null : formData.GroupCodeNo;
        formData.Salesman = formData.Salesman == "0" ? null : formData.Salesman;
        formData.TypeNo = formData.TypeNo == "0" ? null : formData.TypeNo;
        formData.ParentCompanyNo = formData.ParentCompanyNo ? +formData.ParentCompanyNo : null;
        formData.SupplierWarranty = formData.SupplierWarranty ? +formData.SupplierWarranty : null;
        formData.ReviewDate = parseDate(formData.ReviewDate);
        formData.ReviewDate = formData.ReviewDate ? toYMD(formData.ReviewDate) : null;
        formData.upLiftPrice = formData.upLiftPrice == "" ? null : +formData.upLiftPrice;

        return GlobalTrader.ApiClient.putAsync(`/contact/all-companies/${this.companyId}/main-info`, {
            ...formData,
            newSelectIndustryTypes: changedSelecteds,
            unSelectIndustryTypes: changedUnselecteds,
        }, {
            "RequestVerificationToken": this.$form.find('input[name="__RequestVerificationToken"]').val()
        });
    }
}