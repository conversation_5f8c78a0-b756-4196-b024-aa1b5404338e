﻿import { StarRatingEvents } from "../../../../../../../components/star-rating/constants/star-rating-events.constant.js";
import { StarRatingComponent } from "../../../../../../../components/star-rating/star-rating.component.js";
import { CompanyDetailsService } from '../../../company-details-services.js?v=#{BuildVersion}#';

export class EditSalesInformationDialog{
    constructor({ companyId, successCallback }) {
        this.globalLoginClientNo = null;
        this.companySalesInfo = null;
        this.companyId = companyId;
        this.$dialog = null;
        this.$form = $("#edit-sale-info-form");
        this.$editButton = $("#edit-sales-information-btn");
        this.salesInfo = null;
        this.starRatingComponent = null;
        this.successCallback = successCallback;
        this.firstTimeHitApproved = false;
        this.defaultSORating = $('input[name="EditSalesDefaultSORating"]').val();
        this.companyName = $('input[name="EditSalesCompanyName"]').val();
        this.mailGroupId = $('input[name="MailGroupId"]').val();
        this.canNotify = false;
    }

    async initialize(globalClientNo) {
        this.globalLoginClientNo = globalClientNo;
        this._initDialog();
        this._initForm();
        this._setUpForm();
    }

    getData(saleInfo) {
        this.salesInfo = saleInfo;
    }

    _initDialog() {              
        this.$dialog = $('#edit-sales-info-dialog')
            .dialog({
                maxHeight: $(window).height(),
                width: "30vw",
                open: async () => {
                    this._bindDataToForm();
                },
                close: () => {
                    this.$dialog.find(".form-error-summary").hide();
                    this.$form[0].reset();
                    this.$form.validate().resetForm();
                    this._resetDropdown();
                    this.$form.find('.is-invalid').removeClass("is-invalid");
                },
                buttons: [
                    {
                        text: this.save,
                        class: 'btn btn-primary fw-normal',
                        html: `<img src="/img/icons/save.svg" alt="${window.localizedStrings.save}">${window.localizedStrings.save}`,
                        click: async () => {
                            if (this.$form.valid()) {
                                this.$dialog.dialog("setLoading", true);
                                this.$dialog.parent().find(`.ui-dialog-buttonset button:has(img[alt="${window.localizedStrings.save}"])`).prop('disabled', true);
                                this.$dialog.find(".form-error-summary").hide();
                                let response = await CompanyDetailsService.editCompanySalesInfoAsync(this.companyId, this.$form);
                                if (!response?.success) {
                                    showToast("danger", response.title);
                                } else {
                                    this._onUpdateSuccess();
                                    showToast('success', window.localizedStrings.saveChangedMessage);
                                }
                                this.$dialog.parent().find(`.ui-dialog-buttonset button:has(img[alt="${window.localizedStrings.save}"])`).prop('disabled', false);
                                this.$dialog.dialog("setLoading", false);
                                this.$dialog.dialog("close");
                                if (this.successCallback) this.successCallback(this.canNotify);
                                this.canNotify = false;
                            } else {
                                this.$dialog.find(".form-error-summary").show();
                            };
                        }
                    },
                    {
                        text: this.cancel,
                        class: 'btn btn-danger fw-normal',
                        html: `<img src="/img/icons/slash.svg" alt="${window.localizedStrings.cancel}">${window.localizedStrings.cancel}`,
                        click: () => {
                            this.$dialog.dialog("close");
                        },
                    },
                ]
            });
    }

    _initForm() {
        this.$form.validate({
            rules: {
                Salesman: {
                    required: true,
                    min: 1
                },
                SOApproved: {
                    required: false,
                },
                StopStatus: {
                    required: false,
                    maxlength: 10
                },
                SOCurrencyNo: {
                    required: true,
                    min: 1
                },
                CustomerCode: {
                    required: {
                        depends: function () {
                            return $('input[name="SOApproved"]').is(":checked");
                        },
                    },
                    maxlength: 15,
                    normalizer: function (value) {
                        return value.trim(); 
                    }
                },
                SORating: {
                    required: false,
                },
                SOTermsNo: {
                    required: true,
                    min: 1,
                },
                OnStop: {
                    required: false,
                },
                ShippingCharge: {
                    required: false,
                },
                DefaultSOContactNo: {
                    required: false,
                },
                CreditLimit: {
                    required: false,
                },
                ActualCreditLimit: {
                    required: false,
                },
                InsuranceFileNo: {
                    required: false,
                    normalizer: function (value) {
                        return value.trim();
                    },
                    maxlength:70
                },
                InsuredAmountCurrencyNo: {
                    min: {
                        param: 1,
                        depends: function () {
                            return $('input[name="InsuredAmount"]').val() > 0;
                        },
                    },
                },
                InsuredAmount: {
                    required: false,
                },
                WarehouseNo: {
                    required: false,
                },
                CanNotifyMailGroup: {
                    required: false,
                },
                NotesToInvoice: {
                    required: false,
                    maxlength: 500,
                    normalizer: function (value) {
                        return value.trim();
                    }
                }
            },
            messages: {
                Salesman: window.localizedStrings.requiredField,
                SOCurrencyNo: window.localizedStrings.requiredField,
                SOTermsNo: window.localizedStrings.requiredField,
                CustomerCode: { required : window.localizedStrings.requiredField },
                InsuredAmountCurrencyNo: window.localizedStrings.requiredField
            },
            invalidHandler: function (event, validator) {
                if (validator.errorList.length) {
                    $(validator.errorList[0].element).trigger("focus");
                }
            },
        });       
    }

    _setUpEventListener() {
        allowMaxDecimalLength('#edit-sale-info-form input[name="CreditLimit"]', 18, 2, true);
        allowMaxDecimalLength('#edit-sale-info-form input[name="ActualCreditLimit"]', 18, 2, true);
        allowMaxDecimalLength('#edit-sale-info-form input[name="InsuredAmount"]', 18, 2, true);

        this.$form.find('input[name="InsuredAmount"]').on('blur', function () {
            if ($(this).val() === '') $(this).val('0.00');
        });
        this.$form.find('input[name="ActualCreditLimit"]').on('blur', function () {
            if ($(this).val() === '') $(this).val('0.00');
        });
        this.$form.find('input[name="CreditLimit"]').on('blur', function () {
            if ($(this).val() === '') $(this).val('0.00');
        });

        $(this.$form.find('input[name="SOApproved"]').on('change', (e) => {
            const isChecked = e.currentTarget.checked;
            if (isChecked) {
                if (!this.firstTimeHitApproved) {
                    this.starRatingComponent.setStarRatingValue(this.defaultSORating);
                }
                this.canNotify = !this.salesInfo.soApproved;
                $('#required-customer-no-text').removeClass('d-none');
            } else {
                this.canNotify = false;
                $('#required-customer-no-text').addClass('d-none');
            }
            this.firstTimeHitApproved = true;
        }));

        $(this.$form.find('input[name="InsuredAmount"]')).on('input', function () {
            const insuredAmount = parseFloat($(this).val());
            if (insuredAmount > 0) {
                $('#required-insured-currency-text').removeClass('d-none');
            } else {
                $('#required-insured-currency-text').addClass('d-none');
            }
        });
    }

    _setUpForm() {
        this.$form.find('#sale-person-dropdown').dropdown({
            serverside: false,
            endpoint: `/lists/employee`,
            params: {
                GlobalLoginClientNo: this.globalLoginClientNo
            },
            textKey: 'employeeName',
            valueKey: 'loginId',
        });

        this.$form.find('#sell-currency-dropdown').dropdown({
            serverside: false,
            endpoint: `/lists/sell-currencies-for-client`,
            params: {
                GlobalClientLoginNo: this.globalLoginClientNo
            },
            textKey: 'currencyValue',
            valueKey: 'currencyId',
        })

        this.$form.find('#sell-term-dropdown').dropdown({
            serverside: false,
            endpoint: `/lists/sell-incoterms-for-client`,
            params: {
                GlobalLoginClientNo: this.globalLoginClientNo
            },
            textKey: 'name',
            valueKey: 'incotermId',
        })

        this.$form.find('#company-contact-dropdown').dropdown({
            serverside: false,
            endpoint: `/lists/contact/${this.companyId}`,
            textKey: 'contactName',
            valueKey: 'contactId',
        })

        this.$form.find('#insured-currency-dropdown').dropdown({
            serverside: false,
            endpoint: `/lists/sell-currencies-for-client`,
            params: {
                GlobalClientLoginNo: this.globalLoginClientNo
            },
            textKey: 'currencyValue',
            valueKey: 'currencyId',
        })

        this.$form.find('#warehouse-dropdown').dropdown({
            serverside: false,
            endpoint: `/lists/warehouse-for-client`,
            params: {
                GlobalLoginClientNo: this.globalLoginClientNo
            },
            textKey: 'name',
            valueKey: 'id',
        })

        this.starRatingComponent = new StarRatingComponent('sale-info-rating');
        this.starRatingComponent.on(StarRatingEvents.CHANGE, (value) => {
            this.$form.find('input[name="SORating"]').val(value ?? 0);
        })
        this._setUpEventListener();
    }

    _bindDataToForm() {
        $("#sale-person-dropdown").dropdown("select", this.salesInfo.salesman);
        $("#sell-currency-dropdown").dropdown("select", this.salesInfo.soCurrencyNo);
        $("#sell-term-dropdown").dropdown("select", this.salesInfo.soTermsNo);
        $("#company-contact-dropdown").dropdown("select", this.salesInfo.defaultSOContactNo);
        $("#insured-currency-dropdown").dropdown("select", this.salesInfo.insuredAmountCurrencyNo)
        $("#warehouse-dropdown").dropdown("select", this.salesInfo.warehouseNo)

        this.$form.find('input[name="StopStatus"]').val(this.salesInfo.stopStatus);
        this.$form.find('input[name="CustomerCode"]').val(this.salesInfo.customerCode);
        this.$form.find('input[name="CreditLimit"]').val(this.salesInfo.creditLimitRaw);
        this.$form.find('input[name="ActualCreditLimit"]').val(this.salesInfo.actualCreditLimitRaw);
        this.$form.find('input[name="InsuranceFileNo"]').val(this.salesInfo.insuranceFileNo);
        this.$form.find('input[name="InsuredAmount"]').val(this.salesInfo.insuredAmountRaw);
        this.$form.find('input[name="SOApproved"]').attr("checked", this.salesInfo.soApproved);
        this.$form.find('input[name="OnStop"]').attr("checked", this.salesInfo.onStop);
        this.$form.find('input[name="ShippingCharge"]').attr("checked", this.salesInfo.shippingCharge);
        this.$form.find('textarea[name="NotesToInvoice"]').val(this.salesInfo.notesToInvoice);
        this.starRatingComponent.setStarRatingValue(this.salesInfo.soRating);

        $("#credit-limit-so-currency").html(this.salesInfo.soCurrencyCode);
        $("#actual-credit-limit-so-currency").html(this.salesInfo.soCurrencyCode);

        if (this.salesInfo.insuredAmountRaw > 0) {
            $('#required-insured-currency-text').removeClass('d-none');
        }
        if (this.salesInfo.soApproved) {
            $('#required-customer-no-text').removeClass('d-none');
        }
    }

    _resetDropdown() {
        $("#sale-person-dropdown").dropdown("reset");
        $("#sell-currency-dropdown").dropdown("reset");
        $("#sell-term-dropdown").dropdown("reset");
        $("#company-contact-dropdown").dropdown("reset");
        $("#insured-currency-dropdown").dropdown("reset");
        $("#warehouse-dropdown").dropdown("reset");
    }

    _onUpdateSuccess() {
        const stopStatus = $('input[name="StopStatus"]').val()?.trim();
        const statusText = stopStatus ? `[Stop Status: ${stopStatus}]` : '';
        const companyTitle = `${this.companyName} (${this.companyId}) ${statusText}`
        const ratingCount = parseInt($('input[name="SORating"]').val()) || 0;
        const isSOApproved = $('input[name="SOApproved"]').is(':checked');
        const isOnStop = $('input[name=OnStop]').is(':checked');

        $("#company-detail-primary-title").html(companyTitle);
        companyDetailPageInfo.title = companyTitle; // companyDetailsInfo.companyName is defined in script of index.cshtml of CompanyDetail

        const fullStars = '<span class="star fs-12">&#9733;</span>'.repeat(ratingCount);
        const emptyStars = '<span class="star fs-12 empty">&#9733;</span>'.repeat(5 - ratingCount);
        // Set star rating content only when approved or re-rating
        if (isSOApproved) {
            $("#so-rating-title").html(fullStars + emptyStars);
        }
        // Toggle based on SO approval
        $("#approved-customer-wrapper").toggleClass("d-none", !isSOApproved);
        $("#so-approved-label").toggleClass("d-none", !isSOApproved);
        $("#so-un-approved-label").toggleClass("d-none", isSOApproved);

        // Toggle stop status
        $("#company-onStop").toggleClass("d-none", !isOnStop);
    }
}