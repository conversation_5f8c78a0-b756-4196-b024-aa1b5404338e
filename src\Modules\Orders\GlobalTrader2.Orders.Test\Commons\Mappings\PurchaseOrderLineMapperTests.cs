using GlobalTrader2.Dto.PurchaseOrderLine;
using GlobalTrader2.Orders.UseCases.Commons.Mappings;
using GlobalTrader2.Orders.UseCases.PurchaseOrderLine.Models;

namespace GlobalTrader2.Orders.Test.Commons.Mappings
{
    public class PurchaseOrderLineMapperTests
    {
        private readonly IFixture _fixture;
        private readonly IMapper _mapper;

        public PurchaseOrderLineMapperTests()
        {
            _fixture = new Fixture();
            var config = new MapperConfiguration(cfg => cfg.AddProfile<PurchaseOrderLineMapper>());            
            _mapper = config.CreateMapper();
        }

        [Fact]
        public void Map_PurchaseOrderLineDetailsReadModel_ToPurchaseOrderLineDetailsDto_ShouldMapAllProperties()
        {
            // Arrange
            var source = _fixture.Build<PurchaseOrderLineDetailsReadModel>()
                .With(p => p.PurchaseOrderLineId, 9)
                .Create();

            // Act
            var dest = _mapper.Map<PurchaseOrderLineDetailsDto>(source);

            // Assert
            Assert.Equal(source.PurchaseOrderLineId, dest.PurchaseOrderLineId);
            Assert.Equal(source.PurchaseOrderNo, dest.PurchaseOrderNo);
            Assert.Equal(source.FullPart, dest.FullPart);
            Assert.Equal(source.Part, dest.Part);
            Assert.Equal(source.Quantity, dest.Quantity);
            Assert.Equal(Convert.ToDecimal(source.Price), dest.Price);
            Assert.Equal(source.DeliveryDate, dest.DeliveryDate);
            Assert.Equal(source.IpoLineTotal, dest.IpoLineTotal);
            Assert.Equal(source.CurrencyCode, dest.CurrencyCode);
            Assert.Equal(source.ClientCurrencyCode, dest.ClientCurrencyCode);
            Assert.Equal(Convert.ToDecimal(source.LineProfit), dest.LineProfit);
            Assert.Equal(Convert.ToDecimal(source.LineProfitPercentage), dest.LineProfitPercentage);
            Assert.Equal(source.CurrencyNo, dest.CurrencyNo);
            Assert.Equal(source.DateOrdered, dest.DateOrdered);
            Assert.Equal(source.HubCurrencyNo, dest.HubCurrencyNo);
        }
    }
}