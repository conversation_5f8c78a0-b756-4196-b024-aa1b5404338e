using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using FluentAssertions;
using GlobalTrader2.Core.Bases;
using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Core.Models;
using GlobalTrader2.Dto.PurchaseOrder;
using GlobalTrader2.Orders.UserCases.Commons.Mappings;
using GlobalTrader2.Orders.UserCases.Orders.BOM.CompanyAdvisoryNote.CompanyAdvisoryNote.Queries;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetSupplierApprovalPoHistory;
using MediatR;
using Microsoft.Data.SqlClient;
using Moq;
using Xunit;

namespace GlobalTrader2.Orders.Test.Orders.PurchaseOrder.Queries
{
    public class GetSupplierApprovalPoHistoryHandlerTest
    {
        private readonly Mock<IBaseRepository<SupplierApprovalHistoryReadModel>> _mockRepository;
        private readonly Mock<IMediator> _mockMediator;
        private readonly IMapper _mapper;
        private readonly GetSupplierApprovalPoHistoryHandler _handler;

        public GetSupplierApprovalPoHistoryHandlerTest()
        {
            _mockRepository = new Mock<IBaseRepository<SupplierApprovalHistoryReadModel>>();
            var mappingConfig = new MapperConfiguration(config => config.AddProfile<PurchaseOrderMapper>());
            _mapper = mappingConfig.CreateMapper();
            _mockMediator = new Mock<IMediator>();
            _handler = new GetSupplierApprovalPoHistoryHandler(_mockRepository.Object, _mapper, _mockMediator.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnSuccessWithEmptyList_WhenNoDataFound()
        {
            // Arrange
            var query = new GetSupplierApprovalPoHistoryQuery(123, CultureInfo.InvariantCulture);
            var emptyReadModels = new List<SupplierApprovalHistoryReadModel>();

            _mockRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                          .ReturnsAsync(emptyReadModels);
            _mockMediator.Setup(m => m.Send(It.IsAny<GetCompanyAdvisoryNoteQuery>(), It.IsAny<CancellationToken>()))
                        .ReturnsAsync(new BaseResponse<string> { Success = true, Data = string.Empty });


            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            Assert.NotNull(result.Data);
            result.Data.Lines.Should().BeEmpty();
        }

        [Fact]
        public async Task Handle_ShouldReturnMappedData_WhenDataExists()
        {
            // Arrange
            var query = new GetSupplierApprovalPoHistoryQuery(123, CultureInfo.InvariantCulture);
            var readModel = new SupplierApprovalHistoryReadModel
            {
                SupplierId = 456,
                ApprovedDated = DateTime.Parse("2023-01-15", CultureInfo.InvariantCulture) ,
                ApprovalStatus = "Done",
                ApprovedBy = "John Doe",
                SupplierApprovalId = 1,
                SupplierName = "Supplier A"
            };
            var readModels = new List<SupplierApprovalHistoryReadModel> { readModel };


            var advisoryNoteResponse = new BaseResponse<string>
            {
                Success = true,
                Data = "Test advisory note"
            };

            _mockRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                          .ReturnsAsync(readModels);


            _mockMediator.Setup(m => m.Send(It.IsAny<GetCompanyAdvisoryNoteQuery>(), It.IsAny<CancellationToken>()))
                        .ReturnsAsync(advisoryNoteResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            Assert.NotNull(result.Data);
            var returnedDto = result.Data.Lines[0];
            returnedDto.SupplierId.Should().Be(456);
            returnedDto.SupplierAdvisoryNotes.Should().Be("Test advisory note");
            returnedDto.ApprovedDated.Should().NotBeNullOrEmpty();
        }

        [Fact]
        public async Task Handle_ShouldSetEmptyAdvisoryNotes_WhenSupplierIdIsZero()
        {
            // Arrange
            var query = new GetSupplierApprovalPoHistoryQuery(123, CultureInfo.InvariantCulture);
            var readModel = new SupplierApprovalHistoryReadModel
            {
                SupplierId = 0,
                ApprovedDated = DateTime.Parse("2023-01-15", CultureInfo.InvariantCulture)
            };
            var readModels = new List<SupplierApprovalHistoryReadModel> { readModel };


            _mockRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                          .ReturnsAsync(readModels);


            _mockMediator.Setup(m => m.Send(It.IsAny<GetCompanyAdvisoryNoteQuery>(), It.IsAny<CancellationToken>()))
                        .ReturnsAsync(new BaseResponse<string> { Success = true, Data = string.Empty });

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            Assert.NotNull(result.Data);
            result.Data.Lines[0].SupplierAdvisoryNotes.Should().BeEmpty();
        }

        [Fact]
        public async Task Handle_ShouldSetEmptyAdvisoryNotes_WhenMediatorReturnsNull()
        {
            // Arrange
            var query = new GetSupplierApprovalPoHistoryQuery(123, CultureInfo.InvariantCulture);
            var readModel = new SupplierApprovalHistoryReadModel
            {
                SupplierId = 456,
                ApprovedDated = DateTime.Parse("2023-01-15", CultureInfo.InvariantCulture)
            };
            var readModels = new List<SupplierApprovalHistoryReadModel> { readModel };

            var mappedDto = new SupplierApprovalHistoryDto
            {
                SupplierId = 456,
                SupplierName = "Test Supplier"
            };

            var advisoryNoteResponse = new BaseResponse<string>
            {
                Success = true,
                Data = null
            };

            _mockRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                          .ReturnsAsync(readModels);

            _mockMediator.Setup(m => m.Send(It.IsAny<GetCompanyAdvisoryNoteQuery>(), It.IsAny<CancellationToken>()))
                        .ReturnsAsync(advisoryNoteResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
        }

        [Fact]
        public async Task Handle_ShouldProcessMultipleRecords_WhenMultipleDataExists()
        {
            // Arrange
            var query = new GetSupplierApprovalPoHistoryQuery(123, CultureInfo.InvariantCulture);
            var readModels = new List<SupplierApprovalHistoryReadModel>
            {
                new SupplierApprovalHistoryReadModel { SupplierId = 456, ApprovedDated =  DateTime.Parse("2023-01-15", CultureInfo.InvariantCulture)  },
                new SupplierApprovalHistoryReadModel { SupplierId = 789, ApprovedDated =  DateTime.Parse("2023-01-15", CultureInfo.InvariantCulture)  }
            };

            var advisoryNoteResponse = new BaseResponse<string>
            {
                Success = true,
                Data = "Advisory note"
            };

            _mockRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                          .ReturnsAsync(readModels);

            _mockMediator.Setup(m => m.Send(It.IsAny<GetCompanyAdvisoryNoteQuery>(), It.IsAny<CancellationToken>()))
                        .ReturnsAsync(advisoryNoteResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            Assert.NotNull(result.Data);
            result.Data.Lines.Should().HaveCount(2);
            
            // Verify that advisory notes were retrieved for both suppliers
            _mockMediator.Verify(m => m.Send(It.Is<GetCompanyAdvisoryNoteQuery>(q => q.Id == 456), It.IsAny<CancellationToken>()), Times.Once);
            _mockMediator.Verify(m => m.Send(It.Is<GetCompanyAdvisoryNoteQuery>(q => q.Id == 789), It.IsAny<CancellationToken>()), Times.Once);
        }
    }
}