Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Release = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Release.initializeBase(this, [element]);
    this._intBOMID = -1;
    this._UpdatedBy = null;
    this._RequestToPOHubBy = -1;
    this._reqSalespeson = "";
    this._SupportTeamMemberNo = "";
};

Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Release.prototype = {

    get_intBOMID: function () { return this._intBOMID; }, set_intBOMID: function (value) { if (this._intBOMID !== value) this._intBOMID = value; },
    get_BomCode: function () { return this._BomCode; }, set_BomCode: function (value) { if (this._BomCode !== value) this._BomCode = value; },
    get_BomName: function () { return this._BomName; }, set_BomName: function (value) { if (this._BomName !== value) this._BomName = value; },
    get_BomCompanyName: function () { return this._BomCompanyName; }, set_BomCompanyName: function (value) { if (this._BomCompanyName !== value) this._BomCompanyName = value; },
    get_BomCompanyNo: function () { return this._BomCompanyNo; }, set_BomCompanyNo: function (value) { if (this._BomCompanyNo !== value) this._BomCompanyNo = value; },
    get_tblAllReleasedetails: function () { return this._tblAllReleasedetails; }, set_tblAllReleasedetails: function (v) { if (this._tblAllReleasedetails !== v) this._tblAllReleasedetails = v; },
    //get_pnlAllRelease: function () { return this._pnlAllRelease; }, set_pnlAllRelease: function (value) { if (this._pnlAllRelease !== value) this._pnlAllRelease = value; },
    //get_pnlLoadingAllRelease: function () { return this._pnlLoadingAllRelease; }, set_pnlLoadingAllRelease: function (value) { if (this._pnlLoadingAllRelease !== value) this._pnlLoadingAllRelease = value; },
    //get_pnlAllReleaseError: function () { return this._pnlAllReleaseError; }, set_pnlAllReleaseError: function (value) { if (this._pnlAllReleaseError !== value) this._pnlAllReleaseError = value; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Release.callBaseMethod(this, "initialize");
        //this._tblAllReleasedetails.addSelectedIndexChanged(Function.createDelegate(this, this.getSerialDetail));

        this.addShown(Function.createDelegate(this, this.formShown));

    },

    dispose: function () {
        if (this.isDisposed) return;
        this._intBOMID = null;
        this._RequestToPOHubBy = null;
        this._reqSalespeson = null;
        this._SupportTeamMemberNo = null;
        this._tblAllReleasedetails = null;
        //this._pnlAllRelease = null;
        //this._pnlLoadingAllRelease = null;
        //this._pnlAllReleaseError = null;
        Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Release.callBaseMethod(this, "dispose");
    },

    formShown: function () {
        //this.showLoadingAllRelease(false);
        //this.showAllReleaseError(false);
        //this._ctlMainInfo.addMessage("Delivery date and product is required for all attached sourcing results", $R_ENUM$MessageTypeList.Error)
        this._tblAllReleasedetails.clearTable();
        this.getSerialDetail();
        this._tblAllReleasedetails.resizeColumns();

        if (this._blnFirstTimeShown) {
            this._ctlRelease = this.getFieldComponent("ctlRelease");
            this._ctlRelease.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
            this._ctlRelease.addClickNoEvent(Function.createDelegate(this, this.noClicked));
        }

    },

    getSerialDetail: function () {
        //this.showAllReleaseError(false);
        //this.showLoadingAllRelease(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMMainInfo");
        obj.set_DataObject("BOMMainInfo");
        obj.set_DataAction("GetAllSourcingResult");
        obj.addParameter("Bomid", this._intBOMID);
        obj.addDataOK(Function.createDelegate(this, this.getAllReleaseOK));
        obj.addError(Function.createDelegate(this, this.getAllReleaseError));
        obj.addTimeout(Function.createDelegate(this, this.getAllReleaseError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getAllReleaseOK: function (args) {
        res = args._result;
        if (res.Results) {
            this._tblAllReleasedetails.clearTable();
            for (var i = 0; i < res.Results.length; i++) {
                var row = res.Results[i];
                var aryData = [
                    row.REQID
                    , $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part), $R_FN.setCleanTextValue(row.DeliveryDate))
                    , $R_FN.setCleanTextValue(row.BuyPrice)
                    , $R_FN.setCleanTextValue(row.Price)
                ];
                this._tblAllReleasedetails.addRow(aryData, row.REQID, false);
                row = null; aryData = null;
            }
        }
    },

    getAllReleaseError: function (args) {
        //this.showLoadingAllRelease(false);
        //this.showAllReleaseError(true, args.get_ErrorMessage());
    },

    showLoadingAllRelease: function (blnShow) {
        //$R_FN.showElement(this._pnlLoadingAllRelease, blnShow);
        //$R_FN.showElement(this._pnlAllRelease, !blnShow);
        //this.showLoading(blnShow);

    },

    showAllReleaseError: function (blnShow, strMessage) {
        //$R_FN.showElement(this._pnlAllReleaseError, blnShow);
        //if (blnShow) {
        //    this.showLoading(false);
        //    $R_FN.showElement(this._pnlAllRelease, false);
        //    $R_FN.setInnerHTML(this._pnlAllReleaseError, strMessage);
        //}

    },



    yesClicked: function () {
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("Controls/Nuggets/BOMMainInfo");
        obj.set_DataObject("BOMMainInfo");
        obj.set_DataAction("BOMReleaseRequirement");
        obj.addParameter("BomId", this._intBOMID);
        obj.addParameter("BomCode", this._BomCode);
        obj.addParameter("BomName", this._BomName);
        obj.addParameter("BomCompanyName", this._BomCompanyName);
        obj.addParameter("BomCompanyNo", this._BomCompanyNo);
        obj.addParameter("UpdatedBy", this._UpdatedBy);
        obj.addParameter("RequestedBy", this._RequestToPOHubBy);
        obj.addParameter("ReqsalesPerson", this._reqSalespeson);
        obj.addParameter("SupportTeamMemberNo", this._SupportTeamMemberNo);

        obj.addDataOK(Function.createDelegate(this, this.saveReleaseComplete));
        obj.addError(Function.createDelegate(this, this.saveError));
        obj.addTimeout(Function.createDelegate(this, this.saveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        this.onSave();
    },

    noClicked: function () {
        this.showSaving(false);
        this.onNotConfirmed();
    },

    saveError: function (args) {
        this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveReleaseComplete: function (args) {
        if (args._result.Result == true) {
            this.showSavedOK(true);
            this.onSaveComplete();
        }
        else {
            //this._strErrorMessage = args._errorMessage;
            //this.onSaveError();
            this.showSavedOK(true);
            this.onSaveComplete();
        }
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Release.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Release", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
