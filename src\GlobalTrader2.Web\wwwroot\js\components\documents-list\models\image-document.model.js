﻿import { DocumentModel } from '../models/document.model.js?v=#{BuildVersion}#'
import { ApplicationSettingsService } from '../service/document-list.service.js?v=#{BuildVersion}#'

export class ImageDocument extends DocumentModel {
    constructor (id) {
        super(ImageDocument.getApiEndpoint(id), ImageDocument.uploadApiEndpoint(id), ImageDocument.deleteApiEndpoint());
        this.imageUrl = "/api/documents/media";
        this.id = id;
    }

    updateEndpointDocuments(id) {
        this.getDocumentsApiEndpoint = ImageDocument.getApiEndpoint(id);
        this.uploadApiEndpoint = ImageDocument.uploadApiEndpoint(id);
    }

    static getApiEndpoint(id) {
        return ApplicationSettingsService.getImageDocumentsEndpoint(id);
    }

    static uploadApiEndpoint(id) {
        return ApplicationSettingsService.uploadImageDocumentsEndpoint(id);
    }

    static deleteApiEndpoint(id) {
        return ApplicationSettingsService.deleteImageDocumentsEndpoint();
    }

    UpdateRefId(newId){
        this.getDocumentsApiEndpoint = ApplicationSettingsService.getImageDocumentsEndpoint(newId);
        this.id = newId
    }

    async uploadFileAsync($form) {
        const fileInput = $form.find('input[id="FileInput"]')[0];
        const formData = new FormData();

        for (const file of fileInput.files) {
            formData.append('Files', file);
        }

        formData.append('SectionName', $('input[name="sectionName"]').val());
        formData.append('RefId', this.id);
        formData.append('GIId', '');

        $form.find('textarea[name="caption"]').each(function () {
            const captionInput = $(this).val();
            formData.append('Caption', normalizeString(captionInput));
        });

        const headers = {
            'Content-Type': 'multipart/form-data',
        }

        const trimmedFormData = trimFormDataValues(formData);
        const response = await GlobalTrader.ApiClient.postAsync(
            this.uploadApiEndpoint,
            trimmedFormData,
            headers);
        return response;
    }
}

