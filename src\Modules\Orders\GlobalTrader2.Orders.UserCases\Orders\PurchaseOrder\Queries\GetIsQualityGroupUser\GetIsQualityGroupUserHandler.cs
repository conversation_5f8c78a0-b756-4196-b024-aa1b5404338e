﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Core.Domain.Entities;

namespace GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetIsQualityGroupUser
{
    public class GetIsQualityGroupUserHandler : IRequestHandler<GetIsQualityGroupUserQuery, BaseResponse<bool>>
    {
        private readonly IBaseRepository<Core.Domain.Entities.MailGroup> _mailGroupRepository;
        private readonly IBaseRepository<MailGroupMember> _memberRepository;

        public GetIsQualityGroupUserHandler(IBaseRepository<Core.Domain.Entities.MailGroup> mailGroupRepository, IBaseRepository<MailGroupMember> memberRepository)
        {
            _mailGroupRepository = mailGroupRepository;
            _memberRepository = memberRepository;
        }

        /// <summary>
        /// Convert from this stored procedure: [dbo].[usp_Sa_IsQualityGroupUser]
        /// </summary>
        public async Task<BaseResponse<bool>> Handle(GetIsQualityGroupUserQuery request, CancellationToken cancellationToken)
        {
            var mailGroup = _mailGroupRepository.ListAsQueryable()
                .FirstOrDefault(mg => mg.Name == Core.Constants.MailGroup.QualityApproval && mg.ClientNo == request.ClientId);
            if (mailGroup == null)
                return new BaseResponse<bool>()
                {
                    Success = true,
                    Data = false,
                };
            
            return new BaseResponse<bool>() { 
                Success = true,
                Data = await _memberRepository.AnyAsync(m => m.MailGroupNo == mailGroup.MailGroupId && m.LoginNo == request.LoginId)
            };
        }
    }
}
