using GlobalTrader2.Orders.UseCases.Orders.POLine.Queries.GetPurchaseOrderLineAllocations;
using GlobalTrader2.Orders.UseCases.Orders.POLine.Queries.GetPurchaseOrderLineDetails;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.ManufacturerAdvisoryNote.Queries;
using Microsoft.Extensions.Azure;
using NetTopologySuite.Index.HPRtree;
using System.Globalization;
using System.Threading;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Controllers
{
    [ApiController]
    [Authorize]
    [Route("api/orders/purchase-orders/po-lines")]
    public class PurchaseOrderLinesController : ApiBaseController
    {
        private readonly IMediator _mediator;
        private readonly SessionManager _sessionManager;

        public PurchaseOrderLinesController(IMediator mediator, SessionManager sessionManager)
        {
            _mediator = mediator;
            _sessionManager = sessionManager;
        }

        [HttpGet("{purchaseOrderLineId}")]
        public async Task<IActionResult> GetPurchaseOrderLineDetails([FromRoute]int purchaseOrderLineId)
        {
            var result = await _mediator.Send(new GetPurchaseOrderLineDetailsQuery(purchaseOrderLineId));
            if (result.Success && result.Data != null)
            {
                var getManufacturerAdvisoryNote = await _mediator.Send(new GetManufacturerAdvisoryNoteQuery()
                {
                    ClientID = ClientId,
                    ManufacturerId = result.Data.ManufacturerNo.GetValueOrDefault()
                });

                if (getManufacturerAdvisoryNote.Success && getManufacturerAdvisoryNote.Data != null)
                {
                    result.Data.MfrAdvisoryNotes = getManufacturerAdvisoryNote.Data.AdvisoryNotes;
                }

                result.Data.FormatDutyRate = Functions.FormatCurrency(result.Data.DutyRate, CultureInfo.CurrentCulture, null, 5, false);
            }
            
            return Ok(result);
        }

        [HttpGet("{purchaseOrderLineId}/allocations")]
        public async Task<IActionResult> GetAllocationsByPoLineId([FromRoute] int purchaseOrderLineId)
        {
            var result = await _mediator.Send(new GetPurchaseOrderLineAllocationsQuery(purchaseOrderLineId, _sessionManager.IsPOHub, _sessionManager.ClientCurrencyCode));
            return Ok(result);
        }
    }
}