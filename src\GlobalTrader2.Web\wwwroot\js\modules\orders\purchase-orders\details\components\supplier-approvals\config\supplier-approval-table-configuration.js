import { SupplierApprovalTabConstant } from "./supplier-approval-tab-constant.js";
import { ButtonHelper } from '../../../../../../../helper/button-helper.js'

const baseColumnConfig = {
    data: null,
    type: 'string',
    className: 'text-wrap text-break',
}

export const SupplierApprovalTableConfiguration = Object.freeze({
    [SupplierApprovalTabConstant.APPROVAL_STATUS]:{
        tableId:'supplier-approval-approval-status-table',
        columns: [
            {
                data: (row) => row.supplierApprovalId,
                visible: false,
            },
            {
                ...baseColumnConfig,
                data: (row) => (
                    {
                        supplierId: row.supplierId,
                        supplierName: row.supplierName,
                        supplierAdvisoryNotes: row.supplierAdvisoryNotes,
                    }
                ),
                width: '7%',
                title : 'Supplier Name',
                render: (data, type, row, meta) => {
                    return ButtonHelper.nubButton_Company(data.supplierId, data.supplierName, data.supplierAdvisoryNotes)
                }
            },
            {
                ...baseColumnConfig,
                data: 'approvedOrdersCount',
                width: '7%',
                title :'POs in the last 12 months' ,
            },
            {
                ...baseColumnConfig,
                data: 'supplierRMAsCount',
                width: '7%',
                title :'RMAs Last 12 Months', 
            },
            {
                ...baseColumnConfig,
                data: 'purchasingMethod',
                width: '7%',
                title :'Purchaseing Method',
            },
            {
                ...baseColumnConfig,
                data: (row) => (
                    {
                        lineManagerApproval: row.lineManagerApproval,
                        isLineManagerApproved: row.isLineManagerApproved,
                        lineManagerApprovedBy: row.lineManagerApprovedBy,
                        lineManagerApproveDate: row.lineManagerApproveDate
                    }
                ),
                width: '7%',
                title: '<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">Line Manager Approval</div>By (Date Time)',
                render: (data, type, row, meta) => {
                    let lineText = ""
                    if (data.isLineManagerApproved){
                        lineText = `${GlobalTrader.StringHelper.setCleanTextValue(data.lineManagerApprovedBy)} ${data.lineManagerApproveDate}`
                    }
                    return `
                        <div style="min-height: 15px;">${data.lineManagerApproval}</div>
                        <span>${lineText}</span>
                    `;
                }
            },
            {
                ...baseColumnConfig,
                data: (row) => (
                    {
                        qualityApproval: row.qualityApproval,
                        isQualityApproved: row.isQualityApproved,
                        qualityApprovedBy: row.qualityApprovedBy,
                        qualityApproveDate: row.qualityApproveDate
                    }
                ),
                width: '7%',
                title: '<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">Quality Approval</div>By (Date Time)',
                render: (data, type, row, meta) => {
                    let qualityText = "";
                    if (data.isQualityApproved){
                        qualityText = `${GlobalTrader.StringHelper.setCleanTextValue(data.qualityApprovedBy)} ${data.qualityApproveDate}`
                    }

                    return `
                        <div style="min-height: 15px;">${data.qualityApproval}</div>
                        <span>${qualityText}</span>
                    `;
                }
            }
        ]
    },
    [SupplierApprovalTabConstant.SUPPLIER_INFORMATION]: {
        tableId: 'supplier-approval-supplier-information-table',
        columns: [
            {
                data: (row) => row.supplierApprovalId,
                visible: false,
            },
            {
                ...baseColumnConfig,
                data: (row) => (
                    {
                        supplierId: row.supplierId,
                        supplierName: row.supplierName,
                        supplierAdvisoryNotes: row.supplierAdvisoryNotes,
                    }
                ),
                width: '7%',
                title: 'Supplier Name',
                render: (data, type, row, meta) => {
                    return ButtonHelper.nubButton_Company(data.supplierId, data.supplierName, data.supplierAdvisoryNotes)
                }
            },
            {
                ...baseColumnConfig,
                width: '7%',
                data: (row) => (
                    {
                        franchiseweblinkOrEvidence: row.franchiseweblinkOrEvidence,
                        evidenceCount: row.evidenceCount,
                        supplierApprovalId: row.supplierApprovalId
                    }
                ),
                title: '<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">Franchise Weblink or Evidence</div>Attachments',
                render: (data, type, row, meta) => {
                    return `
                        <div style="min-height: 15px;">${data.franchiseweblinkOrEvidence}</div>
                        <a href="javascript:void(0);" style='text-decoration:none;' title="Click to add and view docs">${data.evidenceCount} PDF Uploaded</a>
                    `;
                }
            },
            {
                ...baseColumnConfig,
                width: '10%',
                data: (row) => (
                    {
                        tradeReferenceOne: row.tradeReferenceOne,
                        tradeRefOneCount: row.tradeRefOneCount,
                        supplierApprovalId: row.supplierApprovalId
                    }
                ),
                title: '<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">Trade Reference 1</div>Attachments',
                render: (data, type, row, meta) => {
                    return `
                        <div style="min-height: 15px;">${data.tradeReferenceOne}</div>
                        <a href="javascript:void(0);" style='text-decoration:none;' title="Click to add and view docs">${data.tradeRefOneCount} PDF Uploaded</a>
                    `;
                }
            },
            {
                ...baseColumnConfig,
                width: '10%',
                data: (row) => (
                    {
                        tradeReferenceTwo: row.tradeReferenceTwo,
                        tradeRefTwoCount: row.tradeRefTwoCount,
                        supplierApprovalId: row.supplierApprovalId
                    }
                ),
                title: '<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">Trade Reference 2</div>Attachments', 
                render: (data, type, row, meta) => {
                    return `
                        <div style="min-height: 15px;">${data.tradeReferenceTwo}</div>
                        <a href="javascript:void(0);" style='text-decoration:none;' title="Click to add and view docs">${data.tradeRefTwoCount} PDF Uploaded</a>
                    `;
                }
            },
            {
                ...baseColumnConfig,
                width: '10%',
                data: (row) => (
                    {
                        tradeReferenceThree: row.tradeReferenceThree,
                        tradeRefThreeCount: row.tradeRefThreeCount,
                        supplierApprovalId: row.supplierApprovalId
                    }
                ),
                title: '<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">Trade Reference 3</div>Attachments',
                render: (data, type, row, meta) => {
                    return `
                        <div style="min-height: 15px;">${data.tradeReferenceThree}</div>
                        <a href="javascript:void(0);" style='text-decoration:none;' title="Click to add and view docs">${data.tradeRefThreeCount} PDF Uploaded</a>
                    `;
                }
            },
            {
                ...baseColumnConfig,
                width: '7%',
                data: (row) => row.devicePictureCount,
                title: 'Device Pictures Attachment',
                render: (data, type, row, meta) => {
                    return `
                        <a href="javascript:void(0);" style='text-decoration:none;' title="Click to add and view docs">${data} Attachment Uploaded</a>
                    `;
                }
            },
            {
                ...baseColumnConfig,
                width: '7%',
                data: (row) => row.manufacturerPictureCount,
                title: 'Manufacturer Lebel Pictures Attachment Picture Attachment',
                render: (data, type, row, meta) => {
                    return `
                        <a href="javascript:void(0);" style='text-decoration:none;' title="Click to add and view docs">${data} Attachment Uploaded</a>
                    `;
                }
            },
            {
                ...baseColumnConfig,
                width: '7%',
                data: (row) => row.traceabilityPictureCount,
                title: 'Traceability Pictures Attachment',
                render: (data, type, row, meta) => {
                    return `
                        <a href="javascript:void(0);" style='text-decoration:none;' title="Click to add and view docs">${data} Attachment Uploaded</a>
                    `;
                }
            },
            {
                ...baseColumnConfig,
                width: '7%',
                data: 'status',
                title: 'Status',
            }
        ]

    },
    [SupplierApprovalTabConstant.SUPPLIER_APPROVAL_HISTORY] :{
        tableId: 'supplier-approval-supplier-approval-history-table',
        columns: [
            {
                data:'supplierApprovalId',
                visible: false,
            },
            {
                ...baseColumnConfig,
                width: '7%',
                data: (row) => (
                    {
                        supplierId: row.supplierId,
                        supplierName: row.supplierName,
                        supplierAdvisoryNotes: row.supplierAdvisoryNotes,
                    }
                ),
                title: 'Previous Supplier',
                render: (data, type, row, meta) => {
                    return ButtonHelper.nubButton_Company(data.supplierId, data.supplierName, data.supplierAdvisoryNotes)
                }
            },
            {
                ...baseColumnConfig,
                width: '7%',
                data: 'approvalStatus',
                title: 'Approval Status',
                render: (data, type, row, meta) => {
                    return GlobalTrader.StringHelper.setCleanTextValue(row.approvalStatus);
                }
            },
            {
                ...baseColumnConfig,
                width: '7%',
                data: 'approvedDated',
                title: 'Date',
                render: (data, type, row, meta) => {
                    return GlobalTrader.StringHelper.setCleanTextValue(row.approvedDated);
                }
            },
            {
                ...baseColumnConfig,
                width: '7%',
                data: 'approvedBy',
                title: 'By',
                render: (data, type, row, meta) => {
                    return GlobalTrader.StringHelper.setCleanTextValue(row.approvedBy);
                }
            }
        ]
    }
});