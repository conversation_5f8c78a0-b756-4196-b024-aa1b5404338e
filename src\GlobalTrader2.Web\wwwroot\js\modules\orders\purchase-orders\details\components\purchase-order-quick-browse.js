﻿import { QuickBrowserComponent } from '../../../../../components/quick-browser/quick-browser.component.js';
import { QuickBrowserEvents } from '../../../../../components/quick-browser/constants/quick-browser.constants.js';
import { TextFilterHelper } from "../../../../../helper/text-filter-helper.js";
import { FieldType } from '../../../../../components/table-filter/constants/field-type.constant.js';
import { NumberType } from '../../../../../components/table-filter/constants/number-type.constant.js';
import { ROHSHelper } from '../../../../../helper/rohs-helper.js';

$(async () => {
    const quickBrowseManager = new PurchaseOrderBrowseManager();
    $("#left-nugget-PurchaseOrderQuickBrowse").on("accordionbeforeactivate", async (event, ui) => {
        if (quickBrowseManager.quickBrowseFilter == null) {
            quickBrowseManager.setLoadingQuickBrowse(true);
            await quickBrowseManager.initialize();
            quickBrowseManager.setLoadingQuickBrowse(false);
        }
    });
});

class PurchaseOrderBrowseManager {
    constructor() {
        this.isBuyerFirstLoad = true;
        this.filterStates = JSON.parse($('#purchase-order-quick-browser-component').attr("data-states"));
        this.quickBrowseConfig = JSON.parse($('#purchase-order-quick-browser-component').attr("data-config"));
        this.isGlobalLogin = this.quickBrowseConfig.isGlobalLogin;
        this.isGSA = this.quickBrowseConfig.isGSA;
        this.pageSize = $('#purchaseOrderQuickBrowseTbl').data('default-page-size') || 10;
        this.defaultPageIndex = $('#purchaseOrderQuickBrowseTbl').data('default-page-index');
        this.sortIndex = $('#purchaseOrderQuickBrowseTbl').data('default-order-by') || 1;
        this.sortDirection = $('#purchaseOrderQuickBrowseTbl').data('default-sort-dir') || window.constants.sortASC;
        this.quickBrowseFilter = null;
        this.quickBrowseTable = null;
        this.clientFilterState = {
            countriesEndpoint: 'countries/dropdown-countries',
            buyerEndpoint: 'setup/security-settings/security-users/users-client',
            params: {
                clientNo: null
            }
        };
        this.filterInputs = [
            {
                fieldType: FieldType.SELECT,
                label: `${window.localizedStrings.quickBrowseViewLevel}`,
                name: 'ViewLevel',
                id: 'ViewLevel',
                value: '',
                options: {
                    serverside: false,
                    endpoint: `lists/view-levels/${this.quickBrowseConfig.enmPageId}`,
                    isHideRefresButton: false,
                    isCacheApplied: true,
                    placeholderValue: "",
                    placeholder: null
                }
            },
            {
                fieldType: FieldType.NUMBER,
                label: window.localizedStrings.quickBrowsePurchaseOrder,
                name: 'PONo',
                id: 'PONo',
                value: '',
                attributes: {
                    "data-input-type": "numeric",
                    "data-input-format": "int",
                    "data-input-min": 0,
                    "data-input-max": 2147483647,
                    "data-input-type-allow-empty": true
                },
                extraPros: {
                    numberType: NumberType.INT
                },

            },
           
            {
                fieldType: FieldType.TEXT,
                label: window.localizedStrings.quickBrowsePartNo,
                name: 'Part',
                id: 'Part',
                value: '',
                attributes: {
                    "maxlength": 50
                },
            },
            {
                fieldType: FieldType.DATE,
                label: window.localizedStrings.quickBrowseDateOrderedFrom,
                name: 'DateOrderedFrom',
                id: 'DateOrderedFrom',
                value: '',
            },
            {
                fieldType: FieldType.DATE,
                label: window.localizedStrings.quickBrowseDateOrderedTo,
                name: 'DateOrderedTo',
                id: 'DateOrderedTo',
                value: '',
            },
            {
                fieldType: FieldType.CHECKBOX,
                label: window.localizedStrings.quickBrowseRecentOnly,
                name: 'RecentOnly',
                id: 'RecentOnly',
                value: '',
            },
            {
                fieldType: FieldType.CHECKBOX,
                label: window.localizedStrings.quickBrowsePOHubOnly,
                name: 'PohubOnly',
                id: 'PohubOnly',
                value: '',
            },
            {
                fieldType: FieldType.CHECKBOX,
                label: window.localizedStrings.quickBrowseIncludeClosed,
                name: 'IncludeClosed',
                id: 'IncludeClosed',
                value: '',
            },
            {
                fieldType: FieldType.DATE,
                label: window.localizedStrings.quickBrowseDeliveryDateFrom,
                name: 'DeliveryDateFrom',
                id: 'DeliveryDateFrom',
                value: '',
            },    
            {
                fieldType: FieldType.DATE,
                label: window.localizedStrings.quickBrowseDeliveryDateTo,
                name: 'DeliveryDateTo',
                id: 'DeliveryDateTo',
                value: '',
            },
            {
                fieldType: FieldType.TEXT,
                label: window.localizedStrings.quickBrowseCompany,
                name: 'CMName',
                id: 'CMName',
                value: '',
                attributes: {
                    "maxlength": 50
                },
            },
            {
                fieldType: FieldType.DATE,
                label: window.localizedStrings.quickBrowseExpediteDateFrom,
                name: 'ExpediteDateFrom',
                id: 'ExpediteDateFrom',
                value: '',
            },
            {
                fieldType: FieldType.DATE,
                label: window.localizedStrings.quickBrowseExpediteDateTo,
                name: 'ExpediteDateTo',
                id: 'ExpediteDateTo',
                value: '',
            },
            {
                fieldType: FieldType.TEXT,
                label: window.localizedStrings.quickBrowseContact,
                name: 'Contact',
                id: 'Contact',
                value: '',
                attributes: {
                    "maxlength": 50
                },
            },
            {
                fieldType: FieldType.NUMBER,
                label: window.localizedStrings.quickBrowseIPONo,
                name: 'IPONo',
                id: 'IPONo',
                value: '',
                attributes: {
                    "data-input-type": "numeric",
                    "data-input-format": "int",
                    "data-input-min": 0,
                    "data-input-max": 2147483647,
                    "data-input-type-allow-empty": true
                },
                extraPros: {
                    numberType: NumberType.INT
                },

            },
          
            {
                fieldType: FieldType.SELECT,
                label: window.localizedStrings.quickBrowseBuyerName,
                name: 'BuyerName',
                id: 'BuyerName',
                value: '',
                options: {
                    serverside: false,
                    endpoint: this.clientFilterState.buyerEndpoint,
                    valueKey: 'loginId',
                    textKey: 'employeeName',
                    isHideRefresButton: false,
                    isCacheApplied: true
                },
            },
            {
                fieldType: FieldType.SELECT,
                label: window.localizedStrings.quickBrowseClientName,
                name: 'Client',
                id: 'Client',
                value: '',
                options: {
                    serverside: false,
                    endpoint: '/user-account/clients/active',
                    isHideRefresButton: false,
                    isCacheApplied: true,
                    params: { isIncludeClient: false },
                    placeholderValue: "",
                    onSelected: (data, selectedClient) => {
                        if (!data || !this.isGlobalLogin || this.isBuyerFirstLoad) {
                            this.isBuyerFirstLoad = false;
                            return;
                        };
                        this.clientFilterState.params.clientNo = selectedClient;
                        this.reloadCountryDropdown();
                        this.reloadBuyerDropdown();
                    },
                },
            },
            {
                fieldType: FieldType.SELECT,
                label: window.localizedStrings.quickBrowseCountry,
                name: 'Country',
                id: 'Country',
                value: '',
                options: {
                    serverside: false,
                    endpoint: this.clientFilterState.countriesEndpoint,
                    valueKey: 'countryId',
                    textKey: 'countryName',
                    isHideRefresButton: false,
                    isCacheApplied: true,
                    placeholderValue: "",
                },

            },
            {
                fieldType: FieldType.SELECT,
                label: window.localizedStrings.quickBrowseStatus,
                name: 'PurchaseOrderStatus',
                id: 'PurchaseOrderStatus',
                value: '',
                options: {
                    serverside: false,
                    endpoint: 'lists/purchase-order-status',
                    valueKey: 'id',
                    textKey: 'name',
                    isHideRefresButton: false,
                    isCacheApplied: true
                },
            },
            {
                fieldType: FieldType.SELECT,
                label: window.localizedStrings.quickBrowseChecked,
                name: 'SOStatus',
                id: 'SOStatus',
                value: '',
                options: {
                    serverside: false,
                    endpoint: 'lists/po-approved-status',
                    valueKey: 'id',
                    textKey: 'name',
                    isHideRefresButton: false,
                    isCacheApplied: true,
                    placeholderValue: "",
                },

            },
            {
                fieldType: FieldType.SELECT,
                label: window.localizedStrings.quickBrowseSupplierApproval,
                name: 'SupplierApprovalStatus',
                id: 'SupplierApprovalStatus',
                value: '',
                options: {
                    serverside: false,
                    endpoint: 'lists/supplier-approval-status',
                    valueKey: 'id',
                    textKey: 'name',
                    isHideRefresButton: false,
                    isCacheApplied: true,
                    placeholderValue: "",
                },
            },
            {
                fieldType: FieldType.SELECT,
                label: window.localizedStrings.quickBrowseAS6081,
                name: 'AS6081',
                id: 'AS6081',
                value: '',
                options: {
                    serverside: false,
                    endpoint: 'lists/counterfeit-electronic-parts',
                    valueKey: 'id',
                    textKey: 'name',
                    isHideRefresButton: false,
                    isCacheApplied: true,
                    placeholderValue: "",
                },

            },

        ];
    }

    async initialize() {
        this.initQuickBrowser();
    }

    async initQuickBrowser() {
        this.quickBrowseFilter = new QuickBrowserComponent('#purchase-order-quick-browser-component', {
            inputConfigs: this.filterInputs
        });
        this.quickBrowseFilter.init();
        this.setUpRuleForDateTimeFilter('DeliveryDateFrom', 'DeliveryDateTo');
        this.setUpRuleForDateTimeFilter('DateOrderedFrom', 'DateOrderedTo');
        this.setUpRuleForDateTimeFilter('ExpediteDateFrom', 'ExpediteDateTo');
        this.hideClientName(!this.isGlobalLogin && !this.isGSA);
        this.quickBrowseFilter.setLockOrUnlockFilter(this.quickBrowseConfig.initSaveDataListState);

        Object.values(this.filterStates).forEach(input => {
            if (input.isOn && input.isShown && input.value && input.name) {
                const currentInput = this.toggleFilterInput(true, input.name);
                if (currentInput) {
                    switch (input.fieldType) {
                        case FieldType.TEXT:
                            currentInput.setValue(TextFilterHelper.getFilterValue(input.searchType, input.value));
                            break;
                        case FieldType.NUMBER:
                            currentInput.setState(input);
                            break;

                        default:
                            currentInput.setValue(input.value);
                            break;
                    }
                }
            }
        });

        this.quickBrowseFilter.on(QuickBrowserEvents.SEARCH, (e) => {
            if (this.quickBrowseTable) {
                GlobalTrader.Helper.reloadPagingDatatableServerSide(this.quickBrowseTable, true);
            }
            else {
                $('#purchaseOrderQuickBrowseTbl').removeClass("d-none");
                this.initDataTable();
            }
        });
    }

    initDataTable() {
        this.quickBrowseTable = $('#purchaseOrderQuickBrowseTbl')
            .DataTable({
                serverSide: true,
                ajax:
                {
                    url: '/api/orders/purchase-order/list',
                    type: 'POST',
                    contentType: 'application/json',
                    beforeSend: (xhr) => {
                    },
                    error: function (xhr, status, error) {
                        console.log("AJAX Error: ", status, error);
                    },
                    data: (data) => {
                        let sortDir = data.order.length !== 0 ? GlobalTrader.SortHelper.getSortDirIdByName(data.order[0].dir) : this.sortDirection;
                        const filtersData = this.quickBrowseFilter.getDisplayedFilterValues().filter((input) => input.name != 'ViewLevel')
                            .map((filter) => {
                                if (filter.fieldType === FieldType.NUMBER) {
                                    return { ...filter, lo: filter.low };
                                }
                                return filter;
                            });

                        const viewLevelInput = this.quickBrowseFilter.getInputElementByName('ViewLevel');
                        return JSON.stringify({
                            draw: data.draw,
                            start: data.start,
                            length: data.length,
                            sortDir: sortDir,
                            viewLevel: viewLevelInput.getValue().value,
                            orderBy: data.order.length !== 0 ? data.order[0].column  : this.sortIndex,
                            filters: filtersData,
                            saveStates: this.quickBrowseFilter.isLockFilter
                        });
                    },
                },
                info: true,
                scrollCollapse: true,
                responsive: true,
                select: false,
                displayStart: this.defaultPageIndex * this.pageSize,
                paging: true,
                ordering: true,
                order: [[0, GlobalTrader.SortHelper.getSortDirNameById(this.sortDirection)]],
                columnDefs:
                    [
                        { "orderSequence": [window.constants.sortASCName, window.constants.sortDESCName], "targets": "_all" },
                    ],
                searching: false,
                pageLength: this.pageSize,
                lengthMenu: [5, 10, 25, 50],
                bLengthChange: false,
                dom: "<'dt-layout-start text-center'r>t<'dt-layout-cell text-center'ip>",
                language:
                {
                    emptyTable: `<i>${window.localizedStrings.noMatches}</i>`,
                    zeroRecords: `<i>${window.localizedStrings.noMatches}</i>`,
                    infoFiltered: "",
                    lengthMenu: "_MENU_ per page",
                    info: "Page _PAGE_ of _PAGES_",
                    infoEmpty: "",
                    loadingRecords: ""
                },
                processing: true,
                headerCallback: (thead) => {
                    $(thead).find("th").addClass('align-baseline');
                },
                columns:
                    [

                        {
                            data: (row) => ({
                                purchaseOrderId: row.purchaseOrderId,
                                purchaseOrderNumber: row.purchaseOrderNumber,
                                companyName: row.companyName,
                                quantity: row.quantityOrdered,
                                partNo: row.part,
                                rohs: row.rohs
                            }),
                            type: 'string',
                            className: 'text-wrap text-break',
                            title: "PO",
                            render: (data, type, row) => {
                                let purchaseOrderId = GlobalTrader.StringHelper.setCleanTextValue((DataTable.render.text().display(data.purchaseOrderId)));
                                let purchaseOrderNumber = GlobalTrader.StringHelper.setCleanTextValue((DataTable.render.text().display(data.purchaseOrderNumber)));
                                let companyName = GlobalTrader.StringHelper.setCleanTextValue((DataTable.render.text().display(data.companyName)));
                                let quantity = GlobalTrader.StringHelper.setCleanTextValue((DataTable.render.text().display(data.quantity)));
                                let partNo = GlobalTrader.StringHelper.setCleanTextValue((DataTable.render.text().display(data.partNo)));
                                let partNoText = null;
                                if (quantity && partNo) {
                                    partNoText = `\n${quantity} x ${ROHSHelper.writePartNo(data.partNo, data.rohs)}`

                                }
                                let content = `<b>${purchaseOrderNumber}</b> - ${companyName}`
                                if (partNoText !== null) {
                                    content = content + partNoText
                                }
                                if (content.trim() == '') content = purchaseOrderId;
                                const url = GlobalTrader.PageUrlHelper.Get_Url_PurchaseOrder(data.purchaseOrderId);
                                return `<span><a class="dt-hyper-link" href="${url}">${GlobalTrader.StringHelper.setCleanTextValue(content, true)}</a></span >`;
                            }
                        }
                    ],
                rowId: 'purchaseOrderId',
                createdRow: function (row, data, dataIndex) {
                    $(row).attr('tabindex', '0');
                },
            })
            .on('preXhr.dt', () => {
            })
            .on('draw.dt', () => {
                this.quickBrowseTable.columns.adjust();

                // Remove neutral sorting icon
                const tableId = this.quickBrowseTable.table().node().id;
                $(`#${tableId} thead th`)
                    .removeClass('dt-orderable-asc dt-orderable-desc')
                    .addClass('position-relative');

                $(`#${tableId} thead th:not(.dt-orderable-none)`)
                    .attr('role', 'button');

                $(`#${tableId} thead th .dt-column-order`).addClass('dt-column-order-custom');
            });
    }

    toggleFilterInput(isShow, fieldName) {
        const currentInput = this.quickBrowseFilter.getInputElementByName(fieldName);
        if (currentInput) {
            currentInput.invisible = !isShow;
            isShow ? currentInput.show() : currentInput.hide();

            const currentOption = this.quickBrowseFilter.getOptionByName(fieldName);
            isShow ? currentOption.addClass('selected') : currentOption.removeClass('selected');
            currentOption.find("img").attr(
                "src",
                `/img/icons/${isShow ? 'check-green.svg' : 'xmark-red.svg'}`
            );
        }
        return currentInput;
    }

    setLoadingQuickBrowse(isLoading) {
        if (isLoading) {
            $('#purchase-order-quick-browser-section').prepend(`<div id = "purchase-order-loading-quick-browse-content" class= "text-loader m-auto" ></div>`);
        } else {
            $(`#purchase-order-loading-quick-browse-content`).remove();
            $(`#purchase-order-quick-browser-component`).removeClass("d-none");
        }
    }
    hideClientName(isHide) {
        const input = $('[data-filter-name="Client"]');
        if (isHide) {
            input.hide()
        }
    }

    reloadCountryDropdown() {
        let params = {
            clientId: this.clientFilterState.params.clientNo
        }
        const $countryDropdown = $("#purchase-order-quick-browser-component #Country");
        $countryDropdown.dropdown("reset");
        $countryDropdown.dropdown("reload", this.clientFilterState.countriesEndpoint, params);
    }
    reloadBuyerDropdown() {
        let params = {
            clientNo: this.clientFilterState.params.clientNo
        }
        const $salesmanDropdown = $("#purchase-order-quick-browser-component #BuyerName");
        $salesmanDropdown.dropdown("reset");
        $salesmanDropdown.dropdown("reload", this.clientFilterState.salespersonEndpoint, params);
    }
    setUpRuleForDateTimeFilter(fromDateId, toDateId) {
        $(`#${fromDateId}`).datepicker2({
            dateFormat: "dd/mm/yy",
            beforeShow: function () {
                const date = $(`#${toDateId}`).datepicker("getDate");
                if (!date) {
                    $(`#${fromDateId}`).datepicker2("option", "maxDate", null);
                }
                else {
                    const dateValue = new Date(date);
                    $(`#${fromDateId}`).datepicker2("option", "maxDate", dateValue);

                }
            }
        });

        $(`#${toDateId}`).datepicker2({
            dateFormat: "dd/mm/yy",
            beforeShow: function () {
                const date = $(`#${fromDateId}`).datepicker("getDate");
                if (!date) {
                    $(`#${toDateId}`).datepicker2("option", "minDate", null);

                }
                else {
                    const dateValue = new Date(date);
                    $(`#${toDateId}`).datepicker2("option", "minDate", dateValue);
                }
            }
        });

    }
} 