﻿import { KUBAssistantComponent } from "../../../../components/kub-assistant/kub-assistant.component.js?v=#{BuildVersion}#";
export class KubAssistance {
    constructor(localizations) {
        this.state = {
            kubAssistantAllowed: null,
            kubAssistant: null,
            kubTopQuotesTable: null,
            kubTop3BuyPriceTable: null,
            kubTopCustomerRequirementsTable: null,
            kubStockReverseLogisticsTable: null,
            kubStrategicStockTable: null,
            kubStock20RecentTable: null,
        };
        this.localizations = localizations;
    }

    async initKubAssistanceAsync() {
        if (this.state.kubAssistantAllowed == null) {
            const response = await GlobalTrader.ApiClient.getAsync(`/orders/kub-assistance/setting`, {}, {});
            this.state.kubAssistantAllowed = response?.data?.enabled ?? false;
        }

        if (this.state.kubAssistantAllowed) {
            this.initKubAssistantChatBot();
        }
    }

    initKubAssistantChatBot() {
        this.state.kubAssistant = new KUBAssistantComponent('kub-assistant-container', {
            enabled: false
        });

        this.setupKubReadMoreButtonEvents();
        this.initKubTopQuotesTable();
        this.initKubTop3BuyPriceTable();
        this.initKubTopCustomerRequirementsTable();
        this.initKubStockReverseLogisticsTable();
        this.initKubStrategicStockTable();
        this.initKubStock20RecentTable();
    }

    setupKubReadMoreButtonEvents() {
        const toggleBtn = document.getElementById("kub-read-more-less-btn");
        const moreData = document.getElementById("kub-assistant-more-data-wrapper");
        const icon = toggleBtn.querySelector("img");
        const text = toggleBtn.querySelector("span");

        $('#kub-read-more-less-btn').button()
            .off('click')
            .on('click', async () => {
                const isExpanded = !moreData.classList.contains("d-none");

                if (isExpanded) {
                    // Collapse
                    moreData.classList.add("d-none");
                    icon.src = "/img/icons/plus.svg";
                    icon.alt = "Add icon";
                    text.textContent = this.localizations.readMore;
                } else {
                    // Expand
                    moreData.classList.remove("d-none");
                    icon.src = "/img/icons/minus.svg";
                    icon.alt = "Remove icon";
                    text.textContent = this.localizations.readLess;
                }
            });
    }

    collapseKubMoreDataSection() {
        const toggleBtn = document.getElementById("kub-read-more-less-btn");
        const moreData = document.getElementById("kub-assistant-more-data-wrapper");
        const icon = toggleBtn.querySelector("img");
        const text = toggleBtn.querySelector("span");

        moreData.classList.add("d-none");
        icon.src = "/img/icons/plus.svg";
        icon.alt = "Add icon";
        text.textContent = "Read more";
    }

    collapseKubDetail(className) {
        const button = document.getElementById(`${className}-link`);
        const moreData = document.getElementById(`${className}-content`);

        button.classList.remove("kub-view-details-active");
        moreData.classList.add("d-none");
    }

    collapseAllKubDetails() {
        this.collapseKubDetail("kub-last-quotes");
        this.collapseKubDetail("kub-last-top-buy-price-6-months");
        this.collapseKubDetail("kub-last-customer-requirements");
        this.collapseKubDetail("kub-stock-reverse-logistics");
        this.collapseKubDetail("kub-stock-strategic-stock");
        this.collapseKubDetail("kub-stock-20-recent");
    }

    registerKubViewDetailsEventListener(className) {
        const button = document.getElementById(`${className}-link`);
        const moreData = document.getElementById(`${className}-content`);

        $(`#${className}-row`)
            .off('click')
            .on('click', async () => {
                const isExpanded = !moreData.classList.contains("d-none");

                if (isExpanded) {
                    button.classList.remove("kub-view-details-active");
                    moreData.classList.add("d-none");
                } else {
                    moreData.classList.remove("d-none");
                    button.classList.add("kub-view-details-active");
                }
            });
    }

    closeModal() {
        if (this.state.kubAssistant) {
            this.state.kubAssistant.hideModal();
        }
    }

    disable() {
        if (this.state.kubAssistant) {
            this.state.kubAssistant.hideModal();
            this.state.kubAssistant.disable();
        }
    }

    async loadKubAssistantData(bomItemDetail) {
        if (this.state.kubAssistant.events.modalClosed == null) {
            this.state.kubAssistant
                .on("modalClosed", () => {
                    this.collapseKubMoreDataSection();
                    this.collapseAllKubDetails();
                });
        }

        if (bomItemDetail?.isAllowedEnableKub) {
            const fullPartNo = bomItemDetail.fullPart;

            this.registerKubViewDetailsEventListener("kub-last-quotes");
            this.registerKubViewDetailsEventListener("kub-last-top-buy-price-6-months");
            this.registerKubViewDetailsEventListener("kub-last-customer-requirements");
            this.registerKubViewDetailsEventListener("kub-stock-reverse-logistics");
            this.registerKubViewDetailsEventListener("kub-stock-strategic-stock");
            this.registerKubViewDetailsEventListener("kub-stock-20-recent");

            if (!this.state.kubAssistant?.enabled) {
                this.state.kubAssistant.enable();
            }

            const kubAsyncFuns = [
                this.loadKubTop10Quote(fullPartNo),
                this.loadKubTop20CustomerRequirements(fullPartNo),
            ];

            if (!bomItemDetail.isPOHub) {
                $('.stock-row').removeClass('d-none')
                kubAsyncFuns.push(this.GetKubStockDetails(fullPartNo, bomItemDetail.bomNo));
            }
            else {
                $('#kub-last-top-buy-price-6-months-row').removeClass('d-none')
                kubAsyncFuns.push(this.loadKubTop3BuyPrice(fullPartNo));
            }

            await Promise.all(kubAsyncFuns);

            const noDataText = 'N/A';
            $("#kub-selected-part-no").text(bomItemDetail.part);
            $("#kub-latest-quote-price").html(this.replaceChars(bomItemDetail.kubAssistance.lastQuotedPrice ?? noDataText));
            $("#kub-latest-offer-by-hub").html(this.replaceChars(bomItemDetail.kubAssistance.lastHubprice ?? noDataText));
            $("#kub-win-loss-ratio").text(`${bomItemDetail.kubAssistance.numberQuoteToSalesOrder ?? 0}/${bomItemDetail.kubAssistance.numberOfQuote ?? 0}`);

            $("#kub-last-number-cus-req").html(bomItemDetail.kubAssistance.numberOfRequirement);

            if (!bomItemDetail.kubAssistance.lastestHubRFQId) {
                $('#kub-latest-requirement').html('<span>N/A</span>');
            } else {
                $('#kub-latest-requirement').html(GlobalTrader.DatetimeHelper.formatDate(new Date(bomItemDetail.kubAssistance.lastestHubNumberDate), '-') + '<a class="documentachor" href="../../Ord_BOMDetail.aspx?BOM='
                    + bomItemDetail.kubAssistance.lastestHubRFQId + '" target="_blank">(' + bomItemDetail.kubAssistance.lastestHubRFQName + ')</a>');
            }

            $("#kub-price-last-invoiced").html(this.replaceChars(bomItemDetail.kubAssistance.lastSoldPrice ?? noDataText));
            $("#kub-highest-sales").html(this.replaceChars(bomItemDetail.kubAssistance.lastHighestSoldPrice ?? noDataText));
            $("#kub-lowest-sales").html(this.replaceChars(bomItemDetail.kubAssistance.lastLowestSoldPrice ?? noDataText));
            $("#kub-number-parts-invoiced").html(bomItemDetail.kubAssistance.numberOfInvoice ?? noDataText);
            $('#kub-ihs-data').html(this.replaceChars(bomItemDetail.kubAssistance.ihsResult));
            $('#kub-lytica-data').html(this.replaceChars(bomItemDetail.kubAssistance.lyticaResult));
            $('#kub-last-updated-date').html(bomItemDetail.kubAssistance.lastUpdatedDate);
        } else {
            this.state.kubAssistant.disable();
        }
    }
    initKubTopQuotesTable() {
        this.state.kubTopQuotesTable = $('#kub-last-quotes-table').DataTable({
            language: {
                emptyTable: this.localizations.noQuoteFound,
                zeroRecords: this.localizations.noQuoteFound,
            },
            select: false,
            paging: false,
            ordering: false,
            searching: false,
            scrollCollapse: true,
            info: false,
            autoWidth: false,
            columns: [
                {
                    title: this.localizations.quoteNumber,
                    data: 'quoteNumber',
                },
                {
                    title: this.localizations.quoteQuantity,
                    type: 'string',
                    data: 'quantity',
                },
                {
                    title: this.localizations.quotePrice,
                    data: 'price',
                    width: '45%'
                },
                {
                    title: this.localizations.quoteIsConvertedToSO,
                    data: 'convertedToSO',
                }
            ]
        });
    }
    async loadKubTop10Quote(fullPartNo) {
        this.state.kubTopQuotesTable.clear();
        const topQuotes = await GlobalTrader.ApiClient.getAsync("/orders/kub-assistance/bom/top-quotes", { fullPartNo }, {});
        if (topQuotes?.data) {
            this.state.kubTopQuotesTable.rows.add(topQuotes?.data).draw();
        }
    }
    initKubTop3BuyPriceTable() {
        this.state.kubTop3BuyPriceTable = $('#kub-last-top-buy-price-6-months-table').DataTable({
            language: {
                emptyTable: this.localizations.noPurchaseFound,
                zeroRecords: this.localizations.noPurchaseFound,
            },
            select: false,
            paging: false,
            ordering: false,
            searching: false,
            scrollCollapse: true,
            info: false,
            columns: [
                {
                    title: this.localizations.topBuyPricePOIPO,
                    data: 'poId',
                    render: function (data, type, row) {
                        if (row.isClientPrice) {
                            return `
                                ${$('<a target="_blank">').attr('href', `/Orders/InternalPurchaseOrder/Details?ipo=${row.poId}`).addClass('dt-hyper-link').text(row.poNo + ' (From IPO)').prop('outerHTML')}
                            `;
                        }

                        return `
                            ${$('<a target="_blank">').attr('href', `/Orders/PurchaseOrder/Details?po=${row.poId}`).addClass('dt-hyper-link').text(row.poNo).prop('outerHTML')}
                        `;
                    }
                },
                {
                    title: this.localizations.topBuyPriceDate,
                    type: 'string',
                    data: 'date',
                },
                {
                    title: this.localizations.topBuyPriceDate,
                    type: 'string',
                    data: 'price',
                }
            ]
        });
    }
    async loadKubTop3BuyPrice(fullPartNo) {
        this.state.kubTop3BuyPriceTable.clear();
        const top3BuyPrices = await GlobalTrader.ApiClient.getAsync("/orders/kub-assistance/bom/top-buy-prices", { fullPartNo }, {});
        if (top3BuyPrices?.data && top3BuyPrices?.data.length == 1 && top3BuyPrices?.data[0].poId === "No purchase found") {
            this.state.kubTop3BuyPriceTable.rows.add([]).draw();
        } else {
            this.state.kubTop3BuyPriceTable.rows.add(top3BuyPrices?.data).draw();
        }
    }
    initKubTopCustomerRequirementsTable() {
        this.state.kubTopCustomerRequirementsTable = $('#kub-last-customer-requirements-table').DataTable({
            select: false,
            paging: false,
            ordering: false,
            searching: false,
            scrollCollapse: true,
            info: false,
            language: {
                emptyTable: this.localizations.noQuoteFound,
                zeroRecords: this.localizations.noQuoteFound,
            },
            columns: [
                {
                    title: this.localizations.cusReqNumber,
                    data: 'bomName',
                },
                {
                    title: this.localizations.cusReqQuantity,
                    type: 'string',
                    data: 'quantity',
                },
                {
                    title: this.localizations.cusReqPrice,
                    type: 'string',
                    data: 'price',
                },
                {
                    title: this.localizations.cusReqQuote,
                    data: 'quoted',
                }
            ]
        });
    }
    async loadKubTop20CustomerRequirements(fullPartNo) {
        this.state.kubTopCustomerRequirementsTable.clear();
        const topCustomerRequirements = await GlobalTrader.ApiClient.getAsync("/orders/kub-assistance/bom/top-customer-requirements", { fullPartNo }, {});
        if (topCustomerRequirements?.data && topCustomerRequirements?.data.length == 1 && topCustomerRequirements?.data[0].poId === "No purchase found") {
            return;
        }
        this.state.kubTopCustomerRequirementsTable.rows.add(topCustomerRequirements?.data).draw();
    }

    async GetKubStockDetails(fullPartNo, bomId) {
        const stockDetails = await GlobalTrader.ApiClient.getAsync("/orders/kub-assistance/bom/stock-details", { bomId, fullPartNo }, {});
        if (stockDetails?.data) {
            this.state.kubStockReverseLogisticsTable.clear();
            if (stockDetails.data?.topReverseLogisticsOffer && stockDetails.data.topReverseLogisticsOffer.length > 0 && stockDetails.data.topReverseLogisticsOffer[0].dateAdded != "NOT FOUND") {
                this.state.kubStockReverseLogisticsTable.rows.add(stockDetails.data.topReverseLogisticsOffer).draw();
            } else {
                this.state.kubStockReverseLogisticsTable.rows.add([]).draw();
            }

            this.state.kubStrategicStockTable.clear();
            if (stockDetails.data?.topStrategicStockOffer && stockDetails.data.topStrategicStockOffer.length > 0 && stockDetails.data.topStrategicStockOffer[0].dateAdded != "NOT FOUND") {
                this.state.kubStrategicStockTable.rows.add(stockDetails.data.topStrategicStockOffer).draw();
            } else {
                this.state.kubStrategicStockTable.rows.add([]).draw();
            }

            this.state.kubStock20RecentTable.clear();
            if (stockDetails.data?.topPartRecentStock && stockDetails.data.topPartRecentStock.length > 0 && stockDetails.data.topPartRecentStock[0].stockId != "NOT FOUND") {
                this.state.kubStock20RecentTable.rows.add(stockDetails.data.topPartRecentStock).draw();
            } else {
                this.state.kubStock20RecentTable.rows.add([]).draw();
            }
        }
    }

    initKubStockReverseLogisticsTable() {
        this.state.kubStockReverseLogisticsTable = $('#kub-stock-reverse-logistics-table').DataTable({
            language: {
                emptyTable: this.localizations.noOfferfound,
                zeroRecords: this.localizations.noOfferfound,
            },
            select: false,
            paging: false,
            ordering: false,
            searching: false,
            scrollCollapse: true,
            info: false,
            columns: [
                {
                    title: this.localizations.stockDateAdded,
                    data: 'dateAdded',
                },
                {
                    title: this.localizations.stockQuantity,
                    type: 'string',
                    data: 'quantity',
                },
                {
                    title: this.localizations.stockPrice,
                    data: 'price',
                },
                {
                    title: this.localizations.stockManufacturer,
                    data: 'manufacturer',
                }
            ]
        });
    }
    initKubStrategicStockTable() {
        this.state.kubStrategicStockTable = $('#kub-stock-strategic-stock-table').DataTable({
            language: {
                emptyTable: 'No Offer found',
                zeroRecords: 'No Offer found',
            },
            select: false,
            paging: false,
            ordering: false,
            searching: false,
            scrollCollapse: true,
            info: false,
            columns: [
                {
                    title: this.localizations.stockDateAdded,
                    data: 'dateAdded',
                },
                {
                    title: this.localizations.stockQuantity,
                    type: 'string',
                    data: 'quantity',
                },
                {
                    title: this.localizations.stockPrice,
                    data: 'price',
                },
                {
                    title: this.localizations.stockManufacturer,
                    data: 'manufacturer',
                }
            ]
        });
    }
    initKubStock20RecentTable() {
        this.state.kubStock20RecentTable = $('#kub-stock-20-recent-table').DataTable({
            language: {
                emptyTable: this.localizations.noStockFound,
                zeroRecords: this.localizations.noStockFound,
            },
            select: false,
            paging: false,
            ordering: false,
            searching: false,
            scrollCollapse: true,
            info: false,
            columns: [
                {
                    title: this.localizations.stockStockId,
                    type: 'string',
                    data: 'stockId',
                },
                {
                    title: this.localizations.stockQuantity,
                    type: 'string',
                    data: 'quantity',
                },
                {
                    title: this.localizations.stockPrice,
                    data: 'price',
                    width: '300px'
                },
                {
                    title: this.localizations.stockClient,
                    data: 'client',
                }
            ]
        });
    }
    replaceChars(input) {
        if (!input) {
            return input;
        }
        return input.replaceAll(':QUOTE:', '"').replaceAll(':AND:', '&');
    }
}
