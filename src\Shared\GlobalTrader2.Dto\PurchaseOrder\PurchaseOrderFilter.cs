﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Dto.Datatables;

namespace GlobalTrader2.Dto.PurchaseOrder
{
    public class PurchaseOrderFilter
    {
        [IgnoreSaveState]
        public int? Client { get; set; }
        public string? Part { get; set; }
        public string? CMName { get; set; }
        public string? Contact { get; set; }
        public int? PONoLo { get; set; }
        public int? PONoHi { get; set; }
        public int? PONo { get; set; }
        public int? BuyerName { get; set; }
        public int? Country { get; set; }
        public DateTime? DateOrderedFrom { get; set; }
        public DateTime? DateOrderedTo { get; set; }
        public DateTime? ExpediteDateFrom { get; set; }
        public DateTime? ExpediteDateTo { get; set; }
        public DateTime? DeliveryDateFrom { get; set; }
        public DateTime? DeliveryDateTo { get; set; }
        public bool? IncludeClosed { get; set; }
        public int? IPONoLo { get; set; }
        public int? IPONoHi { get; set; }
        public bool? RecentOnly { get; set; }
        [IgnoreSaveState]
        public bool? PohubOnly { get; set; }
        public int? SOStatus { get; set; }
        public int? PurchaseOrderStatus { get; set; }
        [IgnoreSaveState]
        public int? SupplierApprovalStatus { get; set; }

        public string? AS6081 { get; set; }

    }
}
