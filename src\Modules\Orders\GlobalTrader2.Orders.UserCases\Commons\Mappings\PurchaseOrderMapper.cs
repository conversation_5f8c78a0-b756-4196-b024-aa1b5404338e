using GlobalTrader2.Dto.Orders.PurchaseOrder;
using GlobalTrader2.Dto.PurchaseOrder;
using GlobalTrader2.Core.Helpers;

namespace GlobalTrader2.Orders.UserCases.Commons.Mappings;

public class PurchaseOrderMapper : Profile
{
    public PurchaseOrderMapper()
    {
        CreateMap<PurchaseOrder, PurchaseOrderByNumberDto>().ReverseMap();
        CreateMap<PurchaseOrderDetailsReadModel, PurchaseOrderDetailsDto>()
            .ForMember(x => x.AirWayBill, d => d.MapFrom(x => x.AirWayBillPO))
            .ForMember(x => x.WebsiteAddress, d => d.MapFrom(x => x.Website))
            .ForMember(x => x.CountryWarningMessage, d => d.MapFrom(x => x.WarningText));
        CreateMap<PurchaseOrderDetailsReadModel, PurchaseOrderMainInfoDto>()
            .ForMember(x => x.SupplierName, d => d.MapFrom(x => x.CompanyName))
            .ForMember(x => x.SupplierNo, d => d.MapFrom(x => x.CompanyNo))
            .ForMember(x => x.DateOrderedRaw, d => d.MapFrom(x => x.DateOrdered))
            .ForMember(x => x.DateOrdered, d => d.MapFrom(x => Functions.FormatDate(x.DateOrdered)))
            .ForMember(x => x.ExpediteDate, d => d.MapFrom(x => Functions.FormatDate(x.ExpediteDate)))
            .ForMember(x => x.ExpediteNotes, d => d.MapFrom(x => Functions.ReplaceLineBreaks(x.ExpediteNotes ?? string.Empty)))
            .ForMember(x => x.ShippingAccountNo, d => d.MapFrom(x => x.Account))
            .ForMember(x => x.Notes, d => d.MapFrom(x => Functions.ReplaceLineBreaks(x.Notes ?? string.Empty)))
            .ForMember(x => x.Instructions, d => d.MapFrom(x => Functions.ReplaceLineBreaks(x.Instructions ?? string.Empty)))
            .ForMember(x => x.CountryWarningMessage, d => d.MapFrom(x => Functions.ReplaceLineBreaks(x.WarningText ?? string.Empty)))
            .ForMember(x => x.WebsiteAddress, d => d.MapFrom(x => x.Website))
            .ForMember(x => x.SupplierRMAIds, d => d.Ignore())
            .ForMember(x => x.DebitIds, d => d.Ignore())
            .ForMember(x => x.SupplierRMANumbers, d => d.Ignore())
            .ForMember(x => x.EPRIds, d => d.Ignore())
            .ForMember(x => x.POLineEPRIds, d => d.Ignore())
            .ForMember(x => x.DebitNumbers, d => d.Ignore());

        CreateMap<PoLineWarning, PoWarningDto>();
        CreateMap<PurchaseOrderForPageReadModel, PurchaseOrderForPageDto>();
        CreateMap<SupplierApprovalTradeReferenceReadModel, SupplierApprovalTradeReferenceDto>()
            .ForMember(des => des.IsPOApproved, opt => opt.MapFrom(src => src.POApproved))
            .ForMember(des => des.TradeReferenceThree, opt => opt.MapFrom(src => src.TrandeReferenceThree))
            .ForMember(des => des.TraceabilityPictureCount, opt => opt.MapFrom(src => src.TraceblityPictureCount))
            .ForMember(des => des.SupplierAdvisoryNotes, opt => opt.Ignore())
            .ForMember(des => des.QualityApproveDate, opt => opt.Ignore())
            .ForMember(des => des.LineManagerApproveDate, opt => opt.Ignore())
            .ForMember(des => des.IsQualityGroupUser, opt => opt.Ignore())
            .ForMember(des => des.ClientNo, opt => opt.Ignore())
            .ForMember(des => des.CommentText, opt => opt.MapFrom(src => string.Empty))
            .ForMember(des => des.LineManagerComment, opt => opt.MapFrom(src => string.Empty))
            .ForMember(des => des.QualityComment, opt => opt.MapFrom(src => string.Empty))
            .ForMember(des => des.WarrantyPeriod, src => src.Ignore())
            .ForMember(des => des.ApprovedDate, opt => opt.MapFrom(src => string.Empty))
            .ForMember(des => des.LineManagerApproveDate, opt => opt.MapFrom(src => "()"))
            .ForMember(des => des.QualityApproveDate, opt => opt.MapFrom(src => "()"))
            .ForMember(des => des.SupplierType, opt => opt.MapFrom(src => src.CompanyType));
        CreateMap<ExpediteHistoryReadModel, ExpediteHistoryDto>()
            .ForMember(x => x.Id, d => d.MapFrom(x => x.POExpediteNotesId))
            .ForMember(x => x.Action, d => d.MapFrom(x => Functions.ReplaceLineBreaks(x.ExpediteNotes)))
            .ForMember(x => x.ApprovedBy, d => d.MapFrom(x => x.EmployeeName));
        CreateMap<PoApprovalListReadModel, PoApprovalListDto>();
        CreateMap<PoCompanyHistoryReadModel, PoCompanyHistoryDto>()
            .ForMember(x => x.UpdatedByName, d => d.MapFrom(x => x.EmployeeName));
        CreateMap<PurchaseOrderLineDetailReadModel, PurchaseOrderLineDetailDto>()
            .ForMember(x => x.Price, d => d.MapFrom(x => x.Price))
            .ForMember(x => x.AveragePrice, d => d.MapFrom(x => x.AveragePrice))
            .ForMember(x => x.ClientPrice, d => d.MapFrom(x => x.ClientPrice))
            .ForMember(x => x.ClientEstShipCost, d => d.MapFrom(x => x.ClientEstShipCost))
            .ForMember(x => x.GIShipInCost, d => d.MapFrom(x => x.GIShipInCost))
            .ForMember(x => x.ImportCountryShippingCost, d => d.MapFrom(x => x.ImportCountryShippingCost))
            .ForMember(x => x.ServiceCost, d => d.MapFrom(x => x.ServiceCost))
            .ForMember(x => x.ShipInCost, d => d.MapFrom(x => x.ShipInCost))
            .ForMember(x => x.TaxRate, d => d.MapFrom(x => x.TaxRate));
        CreateMap<SupplierPoApprovalReadModel, SupplierPoApprovalDto>()
            .ForMember(des => des.ClientNo, opt => opt.Ignore())
            .ForMember(des => des.ApprovedDated, opt => opt.MapFrom(src => string.Empty))
            .ForMember(des => des.PartERAIReported, opt => opt.MapFrom(src => src.ApproverPartERAIReported))
            .ForMember(des => des.TradeReferenceOne, opt => opt.MapFrom(src => string.Empty))
            .ForMember(des => des.TradeReferenceTwo, opt => opt.MapFrom(src => string.Empty))
            .ForMember(des => des.TradeRefrenceThree, opt => opt.MapFrom(src => string.Empty))
            .ForMember(des => des.CommentText, opt => opt.MapFrom(src => src.TradeRefComment))
            .ForMember(des => des.IsPOApproved, opt => opt.MapFrom(src => src.POApproved))
            .ForMember(des => des.SupplierType, opt => opt.MapFrom(src => src.CompanyType))
            .ForMember(des => des.IsLineManagerApprovalPermission, opt => opt.MapFrom(src => src.IsLineManagerApprovalPermission == 0));
        CreateMap<SupplierApprovalHistoryReadModel, SupplierApprovalHistoryDto>()
            .ForMember(des => des.ApprovedDated, opt => opt.Ignore());
    }
}
