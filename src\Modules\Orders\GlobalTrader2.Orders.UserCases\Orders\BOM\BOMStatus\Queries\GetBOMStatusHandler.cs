namespace GlobalTrader2.Orders.UserCases.Orders.BOM.BOMStatus.Queries
{
    public class GetBomStatusHandler(
        IBaseRepository<VwBomModel> vwBomRepository) : IRequestHandler<GetBomStatusQuery, BaseResponse<string>>
    {
        private readonly IBaseRepository<VwBomModel> _vwBomRepository = vwBomRepository;

        public async Task<BaseResponse<string>> Handle(GetBomStatusQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<string>();

            var bom = await _vwBomRepository.GetAsync(x => x.BOMId == request.BomId);

            response.Success = true;
            response.Data = bom?.Status;

            return response;
        }
    }
}
