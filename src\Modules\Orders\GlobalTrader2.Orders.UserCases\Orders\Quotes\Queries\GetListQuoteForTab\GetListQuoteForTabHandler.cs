﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Orders.UserCases.Orders.Quotes.Queries.GetListQuoteForTab
{
    public class GetListQuoteForTabHandler : IRequestHandler<GetListQuoteForTabQuery, BaseResponse<List<QuoteReadModel>>>
    {
        private readonly IBaseRepository<QuoteReadModel> _baseRepository;

        public GetListQuoteForTabHandler(IBaseRepository<QuoteReadModel> baseRepository)
        {
            _baseRepository = baseRepository;
        }

        public async Task<BaseResponse<List<QuoteReadModel>>> Handle(GetListQuoteForTabQuery request, CancellationToken cancellationToken)
        {
            var procedureName = StoredProcedures.DataListNugget_QuoteLine;
            var queryStr = $"{procedureName} @ClientId, @TeamId, @DivisionId, @LoginId, @OrderBy, @SortDir, @PageIndex, " +
                $"@PageSize, @PartSearch, @ContactSearch, @CMSearch, @SalesmanSearch, @IncludeClosed, @QuoteNoLo, @QuoteNoHi, " +
                $"@DateQuotedFrom, @DateQuotedTo, @RecentOnly, @Important, @TotalLo, @TotalHi, @QuoteStatus, @TotalProfitLo, " +
                $"@TotalProfitHi, @AS6081, @SelectedClientNo, @SelectedLoginID";
            SqlParameter[] param =
            [
                new SqlParameter("@ClientId", SqlDbType.Int) { Value = request.ClientId },
                new SqlParameter("@TeamId", SqlDbType.Int) { Value = (object?)request.TeamId ?? DBNull.Value },
                new SqlParameter("@DivisionId", SqlDbType.Int) { Value = (object?)request.DivisionId ?? DBNull.Value },
                new SqlParameter("@LoginId", SqlDbType.Int) { Value = (object?)request.LoginId ?? DBNull.Value },
                new SqlParameter("@OrderBy", SqlDbType.Int) { Value = request.OrderBy },
                new SqlParameter("@SortDir", SqlDbType.Int) { Value = request.SortDir },
                new SqlParameter("@PageIndex", SqlDbType.Int) { Value = request.PageIndex },
                new SqlParameter("@PageSize", SqlDbType.Int) { Value = request.PageSize },
                new SqlParameter("@PartSearch", SqlDbType.NVarChar, 50) { Value = (object?)request.PartSearch ?? DBNull.Value },
                new SqlParameter("@ContactSearch", SqlDbType.NVarChar, 50) { Value = (object?)request.ContactSearch ?? DBNull.Value },
                new SqlParameter("@CMSearch", SqlDbType.NVarChar, 50) { Value = (object?)request.CMSearch ?? DBNull.Value },
                new SqlParameter("@SalesmanSearch", SqlDbType.Int) { Value = (object?)request.SalesmanSearch ?? DBNull.Value },
                new SqlParameter("@IncludeClosed", SqlDbType.Bit) { Value = request.IncludeClosed },
                new SqlParameter("@QuoteNoLo", SqlDbType.Int) { Value = (object?)request.QuoteNoLo ?? DBNull.Value },
                new SqlParameter("@QuoteNoHi", SqlDbType.Int) { Value = (object?)request.QuoteNoHi ?? DBNull.Value },
                new SqlParameter("@DateQuotedFrom", SqlDbType.DateTime) { Value = (object?)request.DateQuotedFrom ?? DBNull.Value },
                new SqlParameter("@DateQuotedTo", SqlDbType.DateTime) { Value = (object?)request.DateQuotedTo ?? DBNull.Value },
                new SqlParameter("@RecentOnly", SqlDbType.Bit) { Value = request.RecentOnly },
                new SqlParameter("@Important", SqlDbType.Bit) { Value = (object?)request.Important ?? DBNull.Value },
                new SqlParameter("@TotalLo", SqlDbType.Float) { Value = (object?)request.TotalLo ?? DBNull.Value },
                new SqlParameter("@TotalHi", SqlDbType.Float) { Value = (object?)request.TotalHi ?? DBNull.Value },
                new SqlParameter("@QuoteStatus", SqlDbType.Int) { Value = (object?)request.QuoteStatus ?? DBNull.Value },
                new SqlParameter("@TotalProfitLo", SqlDbType.Float) { Value = (object?)request.TotalProfitLo ?? DBNull.Value },
                new SqlParameter("@TotalProfitHi", SqlDbType.Float) { Value = (object?)request.TotalProfitHi ?? DBNull.Value },
                new SqlParameter("@AS6081", SqlDbType.Bit) { Value = (object?)request.AS6081 ?? DBNull.Value },
                new SqlParameter("@SelectedClientNo", SqlDbType.Int) { Value = (object?)request.SelectedClientNo ?? DBNull.Value },
                new SqlParameter("@SelectedLoginID", SqlDbType.Int) { Value = (object?)request.SelectedLoginID ?? DBNull.Value },
            ];                                                                                          

            var quotes = await _baseRepository.SqlQueryRawAsync(queryStr, param);

            return new BaseResponse<List<QuoteReadModel>>
            {
                Success = true,
                Data = quotes is not null && quotes.Any() ? quotes.ToList() : null
            };
        }
    }
}
