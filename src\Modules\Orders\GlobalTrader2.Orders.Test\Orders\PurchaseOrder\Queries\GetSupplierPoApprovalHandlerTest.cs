using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using AutoMapper;
using FluentAssertions;
using GlobalTrader2.Core.Bases;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Core.Models;
using GlobalTrader2.Dto.PurchaseOrder;
using GlobalTrader2.Orders.UserCases.Commons.Mappings;
using GlobalTrader2.Orders.UserCases.Orders.BOM.CompanyAdvisoryNote.CompanyAdvisoryNote.Queries;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetIsQualityGroupUser;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetSuppplierPoApproval;
using MediatR;
using Microsoft.Data.SqlClient;
using Moq;
using Xunit;

namespace GlobalTrader2.Orders.Test.Orders.PurchaseOrder.Queries
{
    public class GetSupplierPoApprovalHandlerTest
    {
        private readonly Mock<IBaseRepository<SupplierPoApprovalReadModel>> _mockRepository;
        private readonly IMapper _mapper;
        private readonly Mock<IMediator> _mockMediator;
        private readonly GetSupplierPoApprovalHandler _handler;

        public GetSupplierPoApprovalHandlerTest()
        {
            _mockRepository = new Mock<IBaseRepository<SupplierPoApprovalReadModel>>();
            var mapperConfig = new MapperConfiguration(
                config => config.AddProfile<PurchaseOrderMapper>());
            _mapper = mapperConfig.CreateMapper();
            _mockMediator = new Mock<IMediator>();
            _handler = new GetSupplierPoApprovalHandler(_mockRepository.Object, _mapper, _mockMediator.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnSuccessWithEmptyList_WhenNoDataFound()
        {
            // Arrange
            var query = new GetSupplierPoApprovalQuery(123, 1, 1, CultureInfo.InvariantCulture);
            var emptyReadModels = new List<SupplierPoApprovalReadModel>();

            _mockRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                          .ReturnsAsync(emptyReadModels);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data!.Count.Should().Be(0);
            result.Data.Lines.Should().BeEmpty();
        }

        [Fact]
        public async Task Handle_ShouldReturnProcessedData_WhenSingleRecordExists()
        {
            // Arrange
            var query = new GetSupplierPoApprovalQuery(123, 1, 1, CultureInfo.InvariantCulture);
            var readModel = new SupplierPoApprovalReadModel
            {
                SupplierId = 456,
                WarrantyPeriod = 365,
                QualityDLUP = DateTime.Parse("2023-01-15", CultureInfo.InvariantCulture),
                LineManagerDLUP = DateTime.Parse("2023-01-15", CultureInfo.InvariantCulture),
                IsSendToQuality = true,
                ApprovedOrdersCount = 1,
                ApproverPartERAIReported = true,
                CompanyType = "Company Type",
                CountryOnHighRisk = 1,
                CustomerDefinedVendor = "",
                GTClinetForPO = "",
                Incoterms = "",
                InDraftMode = true,
                IsLineManagerApprovalPermission = 1,
                IsEscalate = 1,
                LineManagerApproval = "",
                ISEscalationApprovalPermission = true,
                LineManagerApprovedBy = "",
                IsLineManagerApproved = true
            };
            var readModels = new List<SupplierPoApprovalReadModel> { readModel };

            var qualityUserResponse = new BaseResponse<bool>
            {
                Success = true,
                Data = true
            };

            var advisoryNoteResponse = new BaseResponse<string>
            {
                Success = true,
                Data = "Test advisory note"
            };

            _mockRepository.Setup(r => r.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                          .ReturnsAsync(readModels);

            _mockMediator.Setup(m => m.Send(It.IsAny<GetIsQualityGroupUserQuery>(), It.IsAny<CancellationToken>()))
                        .ReturnsAsync(qualityUserResponse);

            _mockMediator.Setup(m => m.Send(It.IsAny<GetCompanyAdvisoryNoteQuery>(), It.IsAny<CancellationToken>()))
                        .ReturnsAsync(advisoryNoteResponse);

            // Act
            var result = await _handler.Handle(query, CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Success.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data!.Count.Should().Be(1);
            result.Data.Lines.Should().HaveCount(1);

            var returnedDto = result.Data.Lines.First();
            returnedDto.SupplierId.Should().Be(456);
            returnedDto.IsQualityUser.Should().BeTrue();
            returnedDto.SupplierAdvisoryNotes.Should().Be("Test advisory note");
            returnedDto.QualityApproveDate.Should().NotBeNullOrEmpty();
            returnedDto.LineManagerApproveDate.Should().BeEmpty(); 
        }
    }
}