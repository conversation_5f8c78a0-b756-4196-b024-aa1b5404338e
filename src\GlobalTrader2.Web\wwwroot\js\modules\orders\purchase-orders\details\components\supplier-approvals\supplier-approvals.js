import { SectionBox } from '../../../../../../components/base/section-box.component.js?v=#{BuildVersion}#';
import { SupplierApprovalTabConstant, SupplierApprovalTabEvent } from './config/supplier-approval-tab-constant.js?v=#{BuildVersion}#';
import { SupplierApprovalTabManager } from './components/supplier-approval-tab.js?v=#{BuildVersion}#';
import { ResizeDatatableEvents } from '../../../../../../components/base/resize-lite-datatable/resize-lite-datatable-events.constanst.js?v=#{BuildVersion}#';

export class SupplierApprovalsManager {
    constructor(purchaseOrderId) {
        this.purchaseOrderId = purchaseOrderId;
        this.$tabContainer = $('#supplier-approval-tabs');
        this.$approvalStatusTab = this.$tabContainer.find('#supplier-approval-approval-status');
        this.currentTabLevel = SupplierApprovalTabConstant.APPROVAL_STATUS;
        this.tabManagers = new Map();
        this.sectionBox = null;
        this.supplierEraiReportedMessage = $('#supplier-erai-reported-message');
        this.productEraiReportedMessage = $('#product-erai-reported-message');
    }

    async initialize() {
        this._setUpSectionBox();
        this._setupTabsEventListener();
        await this._createOrRefreshTab(this.currentTabLevel);
        this.sectionBox.stopLoading(false);
        this.$approvalStatusTab.addClass("active");
        this.$tabContainer.find(`button[data-view-level="${SupplierApprovalTabConstant.APPROVAL_STATUS}"]`).trigger('shown.bs.tab');
    }

    async refreshSectionBox() {
        this.sectionBox.loading(false);
        await this.refreshCurrentTab();
        this.sectionBox.stopLoading(false);
    }

    _setUpSectionBox() {
        this.sectionBox = new SectionBox("#supplier-approval-box", {
            loadingContentId: 'supplier-approval-wrapper',
            loading: true
        });
        this.sectionBox.init();
        this.sectionBox.on('onRefreshed.msb', async () => {
            await this.refreshSectionBox();
        });
    }

    async refreshCurrentTab() {
        const currentTabManager = this.tabManagers.get(this.currentTabLevel);
        if (currentTabManager) {
            await currentTabManager.refreshTabData();
        }
    }

    _setupTabsEventListener() {
        this.$tabContainer.find('button[data-bs-toggle="tab"]').on('shown.bs.tab', async (e) => {
            const newTabLevel = $(e.currentTarget).data("view-level");
            this.currentTabLevel = newTabLevel;
            this._toggleWarningMessage(this.currentTabLevel === SupplierApprovalTabConstant.APPROVAL_STATUS);
            await this._createOrRefreshTab(this.currentTabLevel);
        });
    }

    async _createOrRefreshTab(tabLevel) {
        const validTabLevels = [
            SupplierApprovalTabConstant.APPROVAL_STATUS,
            SupplierApprovalTabConstant.SUPPLIER_INFORMATION,
            SupplierApprovalTabConstant.SUPPLIER_APPROVAL_HISTORY
        ];

        if (tabLevel === undefined || tabLevel === null || !validTabLevels.includes(tabLevel)) {
            return;
        }

        this.sectionBox.loading(false);

        let tabManager = this.tabManagers.get(tabLevel);

        if (tabManager) {
            await tabManager.refreshTabData();
        } else {
            tabManager = new SupplierApprovalTabManager(this.purchaseOrderId, tabLevel);
            await tabManager.initialize();
            this._setupTabManagerEventListeners(tabManager);
            tabManager.initializeTable();
            this.tabManagers.set(tabLevel, tabManager);
        }  
    }

    _setupTabManagerEventListeners(tabManager) {
        tabManager.on(ResizeDatatableEvents.SELECT_DESELECT, (selectedRows) => {
        });
        tabManager.tableManager.on(ResizeDatatableEvents.DRAW, (selectedRows) => {
            this.sectionBox.stopLoading(false);
        });
        tabManager.on(SupplierApprovalTabEvent.ON_DATA_LOADED, (data) => {
            if (this.currentTabLevel === SupplierApprovalTabConstant.APPROVAL_STATUS) {
                this._handleApprovalStatusTabDataLoaded(data);
            }
        });
    }

    _handleApprovalStatusTabDataLoaded(data){
        this.supplierEraiReportedMessage.toggleClass('d-none', !data.some(line => line.supplierERAIReported));
        this.productEraiReportedMessage.toggleClass('d-none', !data.some(line => line.partIsERAIReported));
    }
    
    _toggleWarningMessage(isVisible) {
        if (isVisible) {
            this.supplierEraiReportedMessage.removeClass('d-none');
            this.productEraiReportedMessage.removeClass('d-none');
        } else {
            this.supplierEraiReportedMessage.addClass('d-none');
            this.productEraiReportedMessage.addClass('d-none');
        }
    }
}