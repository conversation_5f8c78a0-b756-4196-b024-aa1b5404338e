﻿using FluentValidation.TestHelper;
using GlobalTrader2.Aggregator.UseCases.FileUpload.Commands.InsertSourcingImage;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Commands.InsertSourcingImage;

namespace GlobalTrader2.Aggregator.Test.FileUpload.Commands
{

    public class InsertSourcingImageValidatorTests
    {
        private readonly InsertSourcingImageValidator _validator;

        public InsertSourcingImageValidatorTests()
        {
            _validator = new InsertSourcingImageValidator();
        }

        [Fact]
        public async Task Should_Have_Error_When_SourcingNo_Is_Default()
        {
            var model = new InsertSourcingImageCommand { ImageName = "image.jpg" };
            var result = await _validator.TestValidateAsync(model);
            result.ShouldHaveValidationErrorFor(x => x.SourcingNo);
        }

        [Fact]
        public async Task Should_Have_Error_When_ImageName_Is_Null_Or_Empty()
        {
            var model = new InsertSourcingImageCommand { SourcingNo = 1, ImageName = null };
            var result = await _validator.TestValidateAsync(model);
            result.ShouldHaveValidationErrorFor(x => x.ImageName);

            model.ImageName = "";
            result = await _validator.TestValidateAsync(model);
            result.ShouldHaveValidationErrorFor(x => x.ImageName);
        }

        [Fact]
        public async Task Should_Have_Error_When_ImageName_Exceeds_MaxLength()
        {
            var model = new InsertSourcingImageCommand
            {
                SourcingNo = 1,
                ImageName = new string('a', 251)
            };

            var result = await _validator.TestValidateAsync(model);
            result.ShouldHaveValidationErrorFor(x => x.ImageName);
        }

        [Fact]
        public async Task Should_Not_Have_Validation_Errors_When_Valid_Input()
        {
            var model = new InsertSourcingImageCommand
            {
                SourcingNo = 1,
                ImageName = "file.jpg",
                Caption = "valid caption"
            };

            var result = await _validator.TestValidateAsync(model);
            result.ShouldNotHaveValidationErrorFor(x => x.SourcingNo);
            result.ShouldNotHaveValidationErrorFor(x => x.ImageName);
            result.ShouldNotHaveValidationErrorFor(x => x.Caption);
        }
    }
}
