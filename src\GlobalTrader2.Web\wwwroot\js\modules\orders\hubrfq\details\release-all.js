import { LiteFormDialog } from "../../../../components/base/lite-form-dialog.js?v=#{BuildVersion}#";
import { ROHSHelper } from "../../../../helper/rohs-helper.js?v=#{BuildVersion}#";

export class ReleaseAllHandler {
    constructor() {
        this.form = null;
        this.sourcingTable = null;
        this.init();
    }

    init() {
        this.form = new LiteFormDialog("#hubrfq-release-all-dialog", {
            width: '1200px',
            closeWhenSuccess: true,
            url: "/api/orders/bom/release-all",
            buttons: [
                { name: "save", icon: "check", alt: "yes", display: 'Yes' },
                { name: "cancel", icon: "xmark", alt: "no", display: 'No' }
            ],
            dialogOptions: {
                open: (event, ui) => {
                    $('.ui-dialog-titlebar-close').css('display', 'none');
                    $('.ui-menu').addClass('ui-menu-custom');

                    this.initSourcingTable();
                }
            }
        });

        this.form.$form.on('closedWithSuccessedResponse.mf', (e, { response }) => {
            $('#release-btn').prop('disabled', true);
        });
    }

    async initSourcingTable() {
        try {
            if (this.sourcingTable && this.sourcingTable.table) {
                this.sourcingTable.table.ajax.reload();
                return;
            }

            this.sourcingTable = new GlobalTrader.Common.SearchTablePageBase({
                sectionBoxSelector: "#release-sourcing-box",
                tableSelector: "#release-sourcing-table",
                tableOptions: {
                    ajax: `/api/orders/bom/${stateValue.id}/sourcing-for-release`,
                    language: {
                        emptyTable: `<i>${releaseLocalized.noData}</i>`
                    },
                    searching: false,
                    select: false,
                    columnDefs: [
                        { type: 'string', targets: '_all' }
                    ],
                    resizeConfig: {
                        numberOfRowToShow: 5
                    },
                    columns: [
                        {
                            title: releaseLocalized.customerRequirementNo,
                            data: "customerRequirementNo",
                            width: "20%",
                            className: "text-break",
                        },
                        {
                            title: GlobalTrader.DataTablesHelper.createStackedHeader([releaseLocalized.part, localizedTitles.deliveryDate]),
                            data: "part",
                            width: "50%",
                            render: function (data, type, row) {
                                return `${ROHSHelper.writePartNo(row.part, 0)} <br>${row.deliveryDate}`;
                            }
                        },
                        {
                            title: localizedTitles.buyPrice,
                            data: "strBuyPrice",
                            width: "15%",
                        },
                        {
                            title: releaseLocalized.sellPrice,
                            data: "strSellPrice",
                            width: "15%",
                        },
                    ],
                    rowId: "sourcingResultId",
                }
            });

            this.sourcingTable.init();
        } catch (error) {
            console.error("Error initializing Sourcing Table:", error);
            throw error;
        }
    }
}
