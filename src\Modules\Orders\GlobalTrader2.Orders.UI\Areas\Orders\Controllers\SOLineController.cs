﻿using GlobalTrader2.Aggregator.UseCases.Orders.Notify.NotifyEccnSales;
using GlobalTrader2.Aggregator.UseCases.Orders.SalesOrders.GetShippedLinesBySoLineId;
using GlobalTrader2.Dto.Datatables;
using GlobalTrader2.Dto.Orders.Requirements;
using GlobalTrader2.Dto.SalesOrder;
using GlobalTrader2.Dto.SalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.ConvertCurrency;
using GlobalTrader2.Orders.UserCases.Orders.ItemSearch.GetItemSearchSalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseRequisition.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Quotes.Queries.GetQuoteLineDetails;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.CurrentAtDate.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.PartDetail.Queries;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.Queries;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Commands.DeleteAllocationBulk;
using GlobalTrader2.Orders.UserCases.Orders.Requirements.SourcingResult.Queries.GetSourcingResult;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetLineAllocations;
using GlobalTrader2.Orders.UserCases.Orders.Service.GetServiceItemDetail;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.CloseSalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.ConfirmSalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.DeleteSalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.EditSalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.InsertSalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Commands.PostUnpostSalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetInfoForEditAllSoLines;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetOrCreateSOIHSEccnDetail;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetPromiseLog;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSalesOrderClientIdBySoLineId;
using GlobalTrader2.Orders.UserCases.Orders.SOLine.Queries.GetSoLineDetails;
using GlobalTrader2.Orders.UserCases.Orders.Stock.GetStockItemDetail;
using GlobalTrader2.SharedUI;
using Microsoft.Extensions.Localization;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using GlobalTrader2.SharedUI.Helper;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Controllers;

[ApiController]
[Authorize]
[Route("api/orders/sales-order/so-lines")]
public class SOLineController : ApiBaseController
{
    private readonly IMediator _mediator;
    private readonly IMapper _mapper;
    private readonly IStringLocalizer<Misc> _miscLocalizer;
    private readonly IStringLocalizer<Status> _statusLocalizer;
    private readonly SessionManager _sessionManager;
    private readonly SecurityManager _securityManager;
    private readonly SettingManager _settingManager;
    private readonly string _EditDifferentClientSoErrorMessage = "You are not authorized to edit this resource.";
    public SOLineController(IMediator mediator, IMapper mapper, IStringLocalizer<Misc> miscLocalizer,
        IStringLocalizer<Status> statusLocalizer, SessionManager sessionManager, SecurityManager securityManager, SettingManager settingManager)
    {
        _mediator = mediator;
        _mapper = mapper;
        _miscLocalizer = miscLocalizer;
        _sessionManager = sessionManager;
        _securityManager = securityManager;
        _statusLocalizer = statusLocalizer;
        _settingManager = settingManager;
    }

    [HttpPost("get-so-ihs-eccn-detail")]
    public async Task<IActionResult> GetSOIHSEccnDetail([FromBody] SoihsEccnRequest request)
    {
        var response = await _mediator.Send(new GetOrCreateSoihsEccnDetailQuery
        {
            ClientNo = ClientId,
            PartNo = request.PartNo.Trim() ?? string.Empty,
            LoginNo = UserId
        });

        return new JsonResult(response);
    }

    [HttpGet("{salesOrderLineId}")]
    public async Task<IActionResult> GetSoLineDetailsAsync(int salesOrderLineId)
    {
        var response = await _mediator.Send(new GetSoLineDetailsQuery(salesOrderLineId, ClientId, IsPOHub, ClientCurrencyCode));
        return Ok(response);
    }

    [HttpGet("{salesOrderLineId}/promise-reason-logs")]
    public async Task<IActionResult> GetPromiseLogsBySoLineIdAsync(int salesOrderLineId)
    {
        var response = await _mediator.Send(new GetPromiseLogQuery(salesOrderLineId));
        return Ok(response);
    }

    [HttpGet("{salesOrderLineId}/allocations")]
    public async Task<IActionResult> GetSoLineAllocationsAsync(int salesOrderLineId)
    {
        var response = await _mediator.Send(new GetLineAllocationsQuery(salesOrderLineId, IsPOHub, ClientCurrencyCode));
        return Ok(response);
    }



    [HttpPut("close-line")]
    public async Task<IActionResult> CloseSoLineAsync([FromBody] CloseSalesOrderLineRequest request)
    {
        if (!await CheckPermission(request.SalesOrderLineId, null, [SecurityFunction.Orders_SalesOrder_Lines_Close]))
        {
            throw new UnauthorizedAccessException(_EditDifferentClientSoErrorMessage);
        }
        var response = await _mediator.Send(new CloseSalesOrderLineCommand
        {
            SalesOrderLineId = request.SalesOrderLineId,
            ResetQuantity = request.ResetQuantity,
            UpdatedBy = UserId,
        });
        return Ok(response);
    }

    [HttpPut("confirm-lines")]
    public async Task<IActionResult> ConfirmSoLineAsync([FromBody] ConfirmSalesOrderLineRequest request)
    {
        if (!request.SalesOrderLineId.HasValue)
        {
            return BadRequest("SalesOrderLineId is required.");
        }
        var response = await _mediator.Send(new ConfirmSalesOrderLineCommand
        {
            SalesOrderLineId = request.SalesOrderLineId,
            SalesOrderNo = request.SalesOrderNo,
            ConfirmAll = request.ConfirmAll
        });
        return Ok(response);
    }

    [HttpPut("post-unpost")]
    public async Task<IActionResult> PostUnpostSoLineAsync([FromBody] PostUnpostSalesOrderLineRequest request)
    {
        if (!await CheckPermission(request.SalesOrderLineId, null, [SecurityFunction.Orders_SalesOrder_Lines_Post]))
        {
            throw new UnauthorizedAccessException(_EditDifferentClientSoErrorMessage);
        }
        var response = await _mediator.Send(new PostUnpostSOLineCommand
        {
            SalesOrderLineId = request.SalesOrderLineId,
            Posted = request.Posted,
            UpdatedBy = UserId,
        });
        return Ok(response);
    }

    [HttpDelete("{salesOrderLineId}/delete-line")]
    public async Task<IActionResult> DeleteSoLineAsync(int salesOrderLineId)
    {
        if (!await CheckPermission(salesOrderLineId, null, [SecurityFunction.Orders_SalesOrder_Lines_Delete]))
        {
            throw new UnauthorizedAccessException(_EditDifferentClientSoErrorMessage);
        }
        var response = await _mediator.Send(new DeleteSalesOrderLineCommand
        {
            SalesOrderLineId = salesOrderLineId
        });
        return Ok(response);
    }

    [HttpPost("search-requirements")]
    public async Task<IActionResult> SearchRequirementsAsync([FromBody] CustomerRequirementsMainInfoRequest request, CancellationToken cancellation)
    {
        var query = new GetCustomerRequirementsMainInfoQuery()
        {
            ClientId = ClientId,
            OrderBy = request.OrderBy,
            SortDir = request.SortDir,
            PageIndex = request.Index / request.Size,
            PageSize = request.Size,
            PartSearch = request.PartSearch != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.PartSearch) : null,
            CompanySearch = request.CompanySearch != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.CompanySearch) : null,
            CustomerRequirementNoHi = request.CustomerRequirementNoHi,
            CustomerRequirementNoLo = request.CustomerRequirementNoLo,
            IncludeClosed = request.IncludeClosed,
            ReceivedDateFrom = request.ReceivedDateFrom,
            ReceivedDateTo = request.ReceivedDateTo,
        };

        var result = await _mediator.Send(query, cancellation);

        var totalItems = result.Data?.FirstOrDefault()?.RowCnt ?? 0;

        var cultureInfo = new CultureInfo(Culture);

        foreach (var item in result.Data ?? Enumerable.Empty<CustomerRequirementMainInfoDto>())
        {
            item.FormatedPrice = Core.Helpers.Functions.FormatCurrency(item.Price ?? 0, cultureInfo, item.CurrencyCode ?? string.Empty, 5, false);
        }

        var response = new DatatableResponse<IEnumerable<CustomerRequirementMainInfoDto>>()
        {
            Success = result.Success,
            Data = result.Data,
            RecordsTotal = totalItems,
            RecordsFiltered = totalItems,
            Draw = request.Draw
        };

        return Ok(response);
    }

    [HttpPost("item-search")]
    public async Task<IActionResult> GetSalesOrderLineItemSearchAsync([FromBody] GetItemsearchSalesOrderLineRequest request, CancellationToken cancellation)
    {
        var query = new GetItemsearchSalesOrderLineQuery()
        {
            ClientId = ClientId,
            OrderBy = request.OrderBy,
            SortDir = request.SortDir,
            PageIndex = request.Index / request.Size,
            PageSize = request.Size,
            PartSearch = request.PartSearch != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.PartSearch) : null,
            CompanySearch = request.CompanySearch != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.CompanySearch) : null,
            SoNoLo = request.SoNoLo,
            SoNoHi = request.SoNoHi,
            IncludeClosed = request.IncludeClosed,
            DateOrderedFrom = request.DateOrderedFrom,
            DateOrderedTo = request.DateOrderedTo,
            DatePromisedFrom = request.DatePromisedFrom,
            DatePromisedTo = request.DatePromisedTo,
            OnlyFromIPO = request.OnlyFromIPO,
            Salesperson = request.Salesperson
        };

        var result = await _mediator.Send(query, cancellation);

        var totalItems = result.Data?.FirstOrDefault()?.RowCnt ?? 0;

        var cultureInfo = new CultureInfo(Culture);

        foreach (var item in result.Data ?? Enumerable.Empty<GetItemsearchSalesOrderLineDto>())
        {
            item.FormatedPrice = Functions.FormatCurrency(item.Price ?? 0, cultureInfo, item.CurrencyCode ?? string.Empty, 5, false);
        }

        var response = new DatatableResponse<IEnumerable<GetItemsearchSalesOrderLineDto>>()
        {
            Success = result.Success,
            Data = result.Data,
            RecordsTotal = totalItems,
            RecordsFiltered = totalItems,
            Draw = request.Draw
        };

        return Ok(response);
    }

    [HttpPut("{salesOrderLineId}")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> EditSoLineAsync([Range(1, int.MaxValue)] int salesOrderLineId, [FromBody] EditSalesOrderLineRequest request)
    {
        if (!await CheckPermission(salesOrderLineId, null, [SecurityFunction.Orders_SalesOrder_Lines_Edit]))
        {
            throw new UnauthorizedAccessException(_EditDifferentClientSoErrorMessage);
        }
        var command = _mapper.Map<EditSalesOrderLineCommand>(request);
        command.SalesOrderLineId = salesOrderLineId;
        command.UpdatedBy = UserId;
        command.ClientId = ClientId;
        command.LoginEmail = LoginEmail;
        command.LoginFullName = LoginFullName;

        var response = await _mediator.Send(command);
        return Ok(response);
    }

    [HttpPut("edit-all-lines")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> EditAllSoLinesAsync([FromBody] EditAllSalesOrderLinesRequest request)
    {

        var so = (await _mediator.Send(new GetInfoForEditAllSoLinesQuery()
        {
            SalesOrderId = request.SalesOrderId
        })).Data;

        if (so == null) return NotFound($"Not found with ID {request.SalesOrderId}");

        var isDifferentClient = so.ClientNo != ClientId;
        if (!await _securityManager.CheckUserLoginInDatabase())
        {
            throw new UnauthorizedAccessException("Login check failed.");
        }

        if (!await _securityManager.CheckFunctionPermissions(UserId, isDifferentClient && _sessionManager.IsGlobalUser, [SecurityFunction.Orders_SalesOrder_Lines_EditAll]))
        {
            return Forbid("Access denied for this operation.");
        }

        var command = new EditAllSalesOrderLinesCommand
        {
            UpdatedBy = UserId,
            
            SalesOrderNo = request.SalesOrderId,
            IsReasonChanged = request.IsReasonChanged,
            PromiseReasonNo = request.PromiseReasonNo,
            DatePromised = request.DatePromised,

            OldEarliestDatePromised = so.EarliestDatePromised,
            IsSOAutoAuthorized = (await _settingManager.GetSettingItem(ApplicationSettingItem.AutoApproveSO))?.ToLower() == "true",
            IsSOAuthorized = so.IsAuthorized,
            AllowEditDatePromisedBetweenCurrentMonthAndEnd = await _securityManager.CheckFunctionPermissions(UserId, isDifferentClient && _sessionManager.IsGlobalUser, [SecurityFunction.Orders_SalesOrder_Lines_EditDatePromisedAfterChecked])
        };

        var response = await _mediator.Send(command);
        return Ok(response);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> AddSoLineAsync([FromBody] AddSalesOderLineRequest request)
    {
        if (!await CheckPermission(null, request.SalesOrderClientNo, [SecurityFunction.Orders_SalesOrder_Lines_CanAdd_NewLine]))
        {
            throw new UnauthorizedAccessException("You are not authorized to add this resource.");
        }
        var command = _mapper.Map<InsertSalesOrderLineCommand>(request);
        command.UpdatedBy = UserId;
        command.Posted = request.StockNo > 0;
        command.Taxable = "Y";

        var response = await _mediator.Send(command);
        return Ok(response);
    }

    [HttpPost("eccn-sales-notification")]
    public async Task<IActionResult> NotifyEccnSalesAsync([FromBody] NotifyEccnSalesRequest request)
    {
        var command = new NotifyEccnSalesCommand(request.SaleOrderId, request.SaleOrderLineId, UserId, ClientId, LoginEmail, LoginFullName);

        var response = await _mediator.Send(command);
        return Ok(response);
    }

    [HttpGet("{salesOrderLineId}/info-for-add")]
    public async Task<IActionResult> GetSOLineDetailForNewBySalesOrderLineId(int salesOrderLineId, [FromQuery] CreateSalesOrderLineRequest request)
    {
        var soLineDetailsResponse = await _mediator.Send(new GetSalesOrderLineQuery()
        {
            ClientId = ClientId,
            SalesOrderLineId = salesOrderLineId,
            CultureInfo = new CultureInfo(Culture)
        });
        if (soLineDetailsResponse.Success && soLineDetailsResponse.Data != null)
        {
            soLineDetailsResponse.Data.Price = Functions.FormatCurrency(
                await _mediator.Send(new ConvertValueBetweenTwoCurrenciesQuery(soLineDetailsResponse.Data?.PriceDouble,
                soLineDetailsResponse.Data?.CurrencyNo ?? 0, request.SOCurrencyID ?? 0, request.SODate ?? DateTime.Now)
                ), CultureInfo.CurrentCulture, "", 5, false);
            return Ok(soLineDetailsResponse);
        }
        else
        {
            return BadRequest("Failed to retrieve sales order line details.");
        }
    }

    [HttpGet("{salesOrderLineId}/detail")]
    public async Task<IActionResult> GetSOLineDetail(int salesOrderLineId)
    {
        var result = await _mediator.Send(new GetSalesOrderLineQuery()
        {
            ClientId = ClientId,
            SalesOrderLineId = salesOrderLineId,
            CultureInfo = new CultureInfo(Culture)
        });
        if (result.Success && result.Data != null)
        {

            result.Data.FormattedDLUP = LocalizerHelper.FormatDLUP(result.Data.DLUP, result.Data.UpdateByName, _miscLocalizer, CultureInfo.CurrentCulture);

            result.Data.Status = result.Data.QuantityShippedNumber < result.Data.QuantityNumber ? _statusLocalizer["Open"] : _statusLocalizer["Closed"];
        }
        return Ok(result);
    }

    [HttpGet("{salesOrderLineId}/shipped-lines")]
    public async Task<IActionResult> GetShippedLinesBySoLineId(int salesOrderLineId)
    {
        var result = await _mediator.Send(new GetShippedLinesBySoLineIdQuery(salesOrderLineId, ClientId));
        return Ok(result);
    }

    [HttpDelete("allocation/delete-bulk")]
    public async Task<IActionResult> DeleteSOLineAllocationsAsync([FromQuery] string allocationIds)
    {
        int[] allocationIdsToDelete = allocationIds
            .Split('|', StringSplitOptions.RemoveEmptyEntries)
            .Select(id => int.TryParse(id, out var value) ? value : (int?)null)
            .OfType<int>()
            .ToArray();
        var result = await _mediator.Send(new DeleteAllocationBulkCommand(allocationIdsToDelete, UserId));
        return Ok(result);
    }

    [HttpGet("sourcing-results/{sourcingResultId}")]
    public async Task<IActionResult> GetSourcingResultForNewBySourcingResultIdAsync([FromRoute] int sourcingResultId, [FromQuery] CreateSalesOrderLineRequest request)
    {
        var query = new GetSourcingResultByIdQuery
        {
            SourcingResultId = sourcingResultId
        };

        var result = await _mediator.Send(query);

        if (result.Success && result.Data != null)
        {
            if (request.SOCurrencyID == result.Data.CurrencyNo)
            {
                result.Data.FormatPrice = Functions.FormatCurrency(result.Data.Price, CultureInfo.CurrentCulture, "", 5, false);
            }
            else
            {
                var convertPrice = await _mediator.Send(new ConvertValueBetweenTwoCurrenciesQuery(result.Data.Price, result.Data.CurrencyNo.GetValueOrDefault(), request.SOCurrencyID.GetValueOrDefault(), request.SODate));
                result.Data.FormatPrice = Functions.FormatCurrency(convertPrice, CultureInfo.CurrentCulture, "", 5, false);
            }
        }
        return Ok(result);
    }

    [HttpGet("customer-requirements/{customerRequirementId}")]
    public async Task<IActionResult> GetCustomerRequirementForNewByCustomerRequirementIdAsync(int customerRequirementId, [FromQuery] CreateSalesOrderLineRequest request)
    {
        var partDetail = await _mediator.Send(new GetPartDetailQuery()
        {
            CustomerRequirementId = customerRequirementId,
            ClientCurrencyID = _sessionManager.GetInt32(SessionKey.ClientCurrencyID),
            ClientCurrencyCode = _sessionManager.GetString(SessionKey.ClientCurrencyCode)
        });
        var response = new BaseResponse<GetCustomerRequirementDto>
        {
            Success = partDetail.Success,
        };
        response.Data = new GetCustomerRequirementDto
        {
            Quantity = partDetail.Data?.Quantity,
            Part = partDetail.Data?.Part,
            SourcingResultNo = partDetail.Data?.SourcingResultNo,
            Price = Functions.FormatCurrency(await _mediator.Send(new ConvertValueBetweenTwoCurrenciesQuery(partDetail.Data?.Price,
                partDetail.Data?.CurrencyNo ?? 0, request.SOCurrencyID ?? 0, request.SODate ?? DateTime.Now)), CultureInfo.CurrentCulture, "", 5, false),
            DateCode = partDetail.Data?.DateCode,
            ManufacturerNo = partDetail.Data?.ManufacturerNo,
            ManufacturerName = partDetail.Data?.ManufacturerName,
            ProductNo = partDetail.Data?.ProductNo,
            PackageNo = partDetail.Data?.PackageNo,
            Rohs = partDetail.Data?.ROHS,
            RequirementNotes = partDetail.Data?.ReqNotes,
            ProductDescription = partDetail.Data?.ProductDescription,
            Msl = partDetail.Data?.MSL,
            PackageDescription = partDetail.Data?.PackageDescription,
            ECCNCode = partDetail.Data?.ECCNCode,
            AS6081 = partDetail.Data?.AS6081,
        };

        return Ok(response);
    }

    [HttpGet("quote-lines/{quoteLineId}")]
    public async Task<IActionResult> GetQuoteLineByQuoteLineIdAsync([FromRoute] int quoteLineId, [FromQuery] CreateSalesOrderLineRequest request, CancellationToken cancellationToken)
    {
        var quoteLineResponse = await _mediator.Send(new GetQuoteLineDetailsQuery(quoteLineId), cancellationToken);
        var response = new BaseResponse<GetQuoteLineDto>
        {
            Success = quoteLineResponse.Success,
        };
        response.Data = new GetQuoteLineDto
        {
            Part = quoteLineResponse.Data?.Part,
            CustomerPart = quoteLineResponse.Data?.CustomerPart,
            DateCode = quoteLineResponse.Data?.DateCode,
            Price = Functions.FormatCurrency(await _mediator.Send(new ConvertValueBetweenTwoCurrenciesQuery(quoteLineResponse.Data?.Price,
                quoteLineResponse.Data?.CurrencyNo ?? 0, request.SOCurrencyID ?? 0, request.SODate ?? DateTime.Now)), CultureInfo.CurrentCulture, "", 5, false),
            Quantity = quoteLineResponse.Data?.Quantity,
            ManufacturerNo = quoteLineResponse.Data?.ManufacturerNo,
            ManufacturerName = quoteLineResponse.Data?.ManufacturerName,
            ProductNo = quoteLineResponse.Data?.ProductNo,
            ProductDescription = quoteLineResponse.Data?.ProductDescription,
            PackageNo = quoteLineResponse.Data?.PackageNo,
            PackageDescription = quoteLineResponse.Data?.PackageDescription,
            PODelDate = quoteLineResponse.Data?.DeliveryDate,
            IsIPO = quoteLineResponse.Data?.SourcingTable == "PQ" || quoteLineResponse.Data?.SourcingTable == "OFPH"
                || quoteLineResponse.Data?.SourcingTable == "EXPH" || quoteLineResponse.Data?.SourcingTable == "HUBSTK"
                || quoteLineResponse.Data?.SourcingTable == "EPPH" || quoteLineResponse.Data?.SourcingTable == "RLPH",
            IsIPOExist = Convert.ToBoolean(quoteLineResponse.Data?.IsIPOCreated),
            Rohs = quoteLineResponse.Data?.ROHS,
            LineNotes = quoteLineResponse.Data?.Notes,
            Msl = quoteLineResponse.Data?.MSLLevel,
            AS6081 = quoteLineResponse.Data?.AS6081,
        };

        return Ok(response);
    }
    [HttpGet("stocks/{stockId}")]
    public async Task<IActionResult> GetStockDetailForNewByStockIdAsync(int stockId, [FromQuery] CreateSalesOrderLineRequest request)
    {
        var stockDetailsResponse = await _mediator.Send(new GetStockItemDetailQuery(stockId));
        if (stockDetailsResponse.Success && stockDetailsResponse.Data != null)
        {
            var stockData = stockDetailsResponse.Data;
            stockData.Price = Functions.FormatCurrency(stockData.ResalePrice, "");
            stockData.LandedCostString = Functions.FormatCurrency(stockData.LandedCost, _sessionManager.ClientCurrencyCode);
            if (request.SOCurrencyID != _sessionManager.ClientCurrencyID)
            {
                var currentRateResponse = await _mediator.Send(new GetCurrentAtDateQuery
                {
                    CurrencyNo = request.SOCurrencyID ?? 0,
                    DatePoint = request.SODate ?? DateTime.Now
                });

                if (currentRateResponse.Success && currentRateResponse.Data != null)
                {
                    stockData.Price = Functions.FormatCurrency(
                        Functions.ConvertValueFromBaseCurrencyDecimal(stockData.ResalePrice, Convert.ToDecimal(currentRateResponse.Data.ExchangeRate)), "");
                    stockData.LandedCostString = $"{stockData.LandedCostString} ({Functions.FormatCurrency(
                            Functions.ConvertValueFromBaseCurrencyDecimal(stockData.LandedCost, Convert.ToDecimal(currentRateResponse.Data.ExchangeRate)),
                            request.SOCurrencyCode ?? string.Empty)})";
                }
                else
                {
                    return BadRequest("Failed to retrieve current exchange rate.");
                }

            }
            return Ok(stockDetailsResponse);
        }
        else
        {
            return BadRequest("Failed to retrieve stock details.");
        }
    }
    [HttpGet("services/{serviceId}")]
    public async Task<IActionResult> GetServiceDetail(int serviceId, [FromQuery] CreateSalesOrderLineRequest request)
    {
        var serviceDetailsResponse = await _mediator.Send(new GetServiceItemDetailQuery(serviceId));
        if (serviceDetailsResponse.Success && serviceDetailsResponse.Data != null)
        {
            var serviceData = serviceDetailsResponse.Data;
            var currentRateResponse = await _mediator.Send(new GetCurrentAtDateQuery
            {
                CurrencyNo = request.SOCurrencyID ?? 0,
                DatePoint = request.SODate ?? DateTime.Now
            });

            if (currentRateResponse.Success && currentRateResponse.Data != null)
            {
                serviceData.PriceString = Functions.FormatCurrency(
                    Functions.ConvertValueFromBaseCurrencyDecimal(serviceData.Price, Convert.ToDecimal(currentRateResponse.Data.ExchangeRate)), "");
            }
            else
            {
                return BadRequest("Failed to retrieve current exchange rate.");
            }
            serviceData.Msl = "N/A";
            serviceData.ClientCurrencyCode = _sessionManager.ClientCurrencyCode;
            serviceData.CostString = Functions.FormatCurrency(serviceData.Cost, "");

            return Ok(serviceDetailsResponse);
        }
        else
        {
            return BadRequest("Failed to retrieve service details.");
        }
    }

    private async Task<bool> CheckPermission(int? salesOrderLineId, int? salesOrderClientNo, SecurityFunction[] permissions)
    {
        if (!await _securityManager.CheckUserLoginInDatabase())
        {
            return false;
        }

        bool isDifferentClient = false;

        if (salesOrderLineId.HasValue)
        {
            var clientIdResponse = await _mediator.Send(new GetSalesOrderClientIdBySoLineIdQuery(salesOrderLineId.Value));
            if (clientIdResponse.Success && clientIdResponse.Data != 0)
            {
                isDifferentClient = _sessionManager.ClientID != clientIdResponse.Data;
            }
        }
        else if (salesOrderClientNo.HasValue)
        {
            isDifferentClient = _sessionManager.ClientID != salesOrderClientNo.Value;
        }

        return await _securityManager.CheckFunctionPermissions(UserId, isDifferentClient && _sessionManager.IsGlobalUser, permissions.ToList());
    }
}

public record SoihsEccnRequest(string PartNo);
