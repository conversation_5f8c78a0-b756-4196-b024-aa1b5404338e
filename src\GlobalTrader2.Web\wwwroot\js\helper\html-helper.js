﻿GlobalTrader.HtmlHelper = (function () {
    const defaultOptions = {
        width: 14,
        height: 14,
        class: "",
        style: "",
    };
    return {
        createAdvisoryHtml: function ({ advisory, options = {} }) {
            const configOptions = { ...defaultOptions, ...options }
            return `<img 
                        src="/img/icons/circle-exclamation-red.svg"
                        alt="Advisory Notes"
                        class="${configOptions.class}" 
                        width="${configOptions.width}" 
                        height="${configOptions.height}" 
                        title="${advisory}" 
                        style="${configOptions.style}"/>`;
        },
        createStarRatingHtml: function ({ starRatingValue, maxStarRating = 5, style = "" }) {
            if (starRatingValue === undefined || starRatingValue === null) {
                starRatingValue = 0;
            }
            else {
                // Ensure starRatingValue is within bounds
                starRatingValue = Math.max(0, Math.min(starRatingValue, maxStarRating));
            }

            let body = "";
            for (let i = 0; i < maxStarRating; i++) {
                let starClass = i < starRatingValue ? "star" : "star empty";
                body += `<span class="${starClass}" style="${style}">&#9733;</span>`;
            }

            let starRatingContainer =
                `
                    <div class="d-flex star-rating align-items-center">
                        ${body}
                    </div>
                `
            return starRatingContainer;
        },
        createHyperLinkHtml: function ({ url, title }) {
            return `<a class="dt-hyper-link" href="${url}">${title}</a>`
        },
        createHyperLinkOpenInNewTabHtml: function ({ url, title }) {
            return `<a class="dt-hyper-link" target="_blank" href="${url}">${title}</a>`
        },
        createUserDisabledImgHtml: function (options) {
            return `<img 
                        src="/img/icons/user-disabled-icon.png"
                        alt="${options.alt ?? 'user-disabled'}"
                        class="${options.class}" 
                        width="${options.width ?? 12}" 
                        height="${options.height ?? 14}" 
                        style="${options.style}"/>`;
        },
        showStockAvailableNew: function (
            shortDesc,
            quantityInStock,
            quantityOnOrder,
            quantityAllocated,
            quantityAvailable,
            stockDetail
        ) {
            const cleanShortDesc = GlobalTrader.StringHelper.setCleanTextValue(shortDesc);
            const cleanStockDetail = GlobalTrader.StringHelper.setCleanTextValue(stockDetail);

            const alertLabelHtml = `<span class="alert-label">Stock Alert!</span>`;
            const notesHtml = `<div class="notes">Stock across all Clients</div>`;
            const itemHtmlFunction = (label) => `<div class="item" title="${label}">${label}</div>`;

            const dropdownHtml = `
                <div class="dropdown-area" title="${cleanStockDetail}">
                ${cleanShortDesc}&nbsp${alertLabelHtml}
                    <div class="dropdown-content">
                        ${notesHtml}
                        ${itemHtmlFunction(quantityInStock)}
                        ${itemHtmlFunction(quantityOnOrder)}
                        ${itemHtmlFunction(quantityAllocated)}
                        ${itemHtmlFunction(quantityAvailable)}
                    </div>
                </div>
            `;

            return dropdownHtml.trim();
        },
        showProductWarningIndividual: function (strName, blnHazardous, blnOrdViaIPOonly, blnIsRestrictedProd, MsgHazardous, MsgIPO, MsgRestricted) {
            const cleanedName = GlobalTrader.StringHelper.setCleanTextValue(strName);

            if (!blnHazardous && !blnOrdViaIPOonly && !blnIsRestrictedProd) {
                return cleanedName;
            }

            // Build warning spans
            let warningSpans = "";

            if (blnHazardous) {
                const hazardousMsg = MsgHazardous?.replace("&#013;", "\n") || "";
                warningSpans += `<span class="hazardous" title="${hazardousMsg}"></span>`;
            }

            if (blnOrdViaIPOonly) {
                const ipoMsg = MsgIPO?.replace("&#013;", "\n") || "";
                warningSpans += `<span class="hazardous-ipo" title="${ipoMsg}"></span>`;
            }

            if (blnIsRestrictedProd) {
                const restrictedMsg = MsgRestricted?.replace("&#013;", "\n") || "";
                warningSpans += `<span class="hazardous-rh" title="${restrictedMsg}"></span>`;
            }

            return `<div>${cleanedName}${warningSpans}</div>`;
        },
        showIHSECCNCodeDefinition: function (strName, strMessage, strWarningDifferMessage = null) {
            const cleanName = GlobalTrader.StringHelper.setCleanTextValue(strName);
            let str = cleanName;

            if (strMessage) {
                let strAlt = strMessage.replaceAll("<br />", "");
                str = `<div class="ihs-part-status-doc" title="${strAlt}">${cleanName}</div>`;
            }
            if (strWarningDifferMessage) {
                str += `<span style='color:red; margin-left:15px; font-style: italic;'>${strWarningDifferMessage}</span>`;
            }
            return str;
        },
        createFileUploadedIcon(isHasDocument) {
            let html = '';
            if (isHasDocument) {
                html = `<img src='/img/icons/file-pdf.png' width="22" height="22" data-bs-toggle="tooltip" data-bs-placement="top" title="Click to view and add doc">`
            } else {
                html = `<img src='/img/icons/upload-file.svg' width="22" height="22" style="filter: brightness(0.4);" data-bs-toggle="tooltip" data-bs-placement="top" title="Click to add doc">`
            }
            return html;
        }
    };
})();