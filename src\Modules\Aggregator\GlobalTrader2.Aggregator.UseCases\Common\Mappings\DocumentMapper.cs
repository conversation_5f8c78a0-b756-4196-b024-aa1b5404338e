﻿using AutoMapper;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Dto.Document;
using GlobalTrader2.Dto.GlobalSettings.DocumentSize;
using GlobalTrader2.Dto.Manufacturers.Document;

namespace GlobalTrader2.Aggregator.UseCases.Common.Mappings
{
    public class DocumentMapper : Profile
    {
        public DocumentMapper()
        {
            CreateMap<ManufacturerPdf, DocumentDto>()
                .ForMember(dest => dest.DocumentId, opt => opt.MapFrom(src => src.ManufacturerPDFId))
                .ForMember(dest => dest.UpdatedByName, opt => opt.MapFrom(src => src.Login != null ? src.Login.EmployeeName : string.Empty))
                .ForMember(dest => dest.Caption, opt => opt.MapFrom(src => Functions.ReplaceLineBreaks(src.Caption ?? string.Empty)))
                .AfterMap((src, dest, context) =>
                {
                    var culture = context.Items["culture"]?.ToString();
                    dest.DateUploadString = src.DLUP != null ? Functions.FormatDateStaticBST((DateTime)src.DLUP, culture, false, true) : "";
                });

            CreateMap<ManuFacturerExcel, DocumentDto>()
                .ForMember(dest => dest.DocumentId, opt => opt.MapFrom(src => src.ManufacturerExcelId))
                .ForMember(dest => dest.UpdatedByName, opt => opt.MapFrom(src => src.Login != null ? src.Login.EmployeeName : string.Empty))
                .ForMember(dest => dest.Caption, opt => opt.MapFrom(src => Functions.ReplaceLineBreaks(src.Caption ?? string.Empty)))
                .AfterMap((src, dest, context) =>
                {
                    var culture = context.Items["culture"]?.ToString();
                    dest.DateUploadString = src.DLUP != null ? Functions.FormatDateStaticBST((DateTime)src.DLUP, culture, false, true) : "";
                });

            CreateMap<DocumentSize, DocumentSizeDto>().ReverseMap();

            CreateMap<SourcingImage, StockImageDetailsDto>()
                .ForMember(dest => dest.StockImageId, opt => opt.MapFrom(src => src.SourcingImageId))
                .ForMember(dest => dest.StockNo, opt => opt.MapFrom(src => src.SourcingResultNo))
                .ForMember(dest => dest.FileName, opt => opt.MapFrom(src => src.ImageName))
                .ForMember(dest => dest.UpdatedByName, opt => opt.MapFrom(src => src.Login != null ? src.Login.EmployeeName : string.Empty))
                .ForMember(dest => dest.DocumentId, opt => opt.MapFrom(src => src.SourcingImageId))
                .ForMember(dest => dest.Caption, opt => opt.MapFrom(src =>(src.Caption ?? string.Empty).Replace("⏎", "<br />")))
                .AfterMap((src, dest, context) =>
                {
                    var culture = context.Items["culture"]?.ToString();
                    dest.DateUploadString = src.DLUP != null ? Functions.FormatDateStaticBST((DateTime)src.DLUP, culture, false, true) : "";
                });
        }
    }
}
