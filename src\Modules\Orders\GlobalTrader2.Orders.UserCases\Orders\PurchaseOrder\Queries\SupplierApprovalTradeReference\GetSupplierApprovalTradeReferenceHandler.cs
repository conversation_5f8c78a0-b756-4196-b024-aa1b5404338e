﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;
using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Dto.PurchaseOrder;
using GlobalTrader2.Orders.UserCases.Orders.BOM.CompanyAdvisoryNote.CompanyAdvisoryNote.Queries;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetIsQualityGroupUser;

namespace GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.SupplierApprovalTradeReference
{
    public class GetSupplierApprovalTradeReferenceHandler : IRequestHandler<GetSupplierApprovalTradeReferenceQuery, BaseResponse<GetApprovalStatusDataResponse<SupplierApprovalTradeReferenceDto>>>
    {
        private readonly IBaseRepository<SupplierApprovalTradeReferenceReadModel> _repository;
        private readonly IMapper _mapper;
        private readonly IMediator _mediator;
        public GetSupplierApprovalTradeReferenceHandler(IBaseRepository<SupplierApprovalTradeReferenceReadModel> repository, IMapper mapper, IMediator mediator)
        {
            _repository = repository;
            _mapper = mapper;
            _mediator = mediator;
        }

        public async Task<BaseResponse<GetApprovalStatusDataResponse<SupplierApprovalTradeReferenceDto>>> Handle(GetSupplierApprovalTradeReferenceQuery request, CancellationToken cancellationToken)
        {
            var readModels = await _repository.SqlQueryRawAsync(
                sql: $"{StoredProcedures.Select_SupplierApprovalTraderReference} @PurchaseOrderId",
                parameters: [new SqlParameter("@PurchaseOrderId", SqlDbType.Int) { Value = request.PurchaseOrderId }]
            );
            var dtos = _mapper.Map<IEnumerable<SupplierApprovalTradeReferenceDto>>(readModels);
            foreach (var dto in dtos)
            {
                dto.SupplierAdvisoryNotes = Functions.ReplaceLineBreaks(await GetSupplierNotes(dto.SupplierId));
                dto.IsQualityGroupUser = await IsQualityGroupUser(request.LoginId, request.ClientId);
                dto.WarrantyPeriod = $"{dto.WarrantyPeriod} days";
            }

            return new BaseResponse<GetApprovalStatusDataResponse<SupplierApprovalTradeReferenceDto>>
            {
                Success = true,
                Data = new GetApprovalStatusDataResponse<SupplierApprovalTradeReferenceDto>()
                {
                    Count = dtos.Count(),
                    Lines = dtos.ToList()
                }
            };
        }

        private async Task<string> GetSupplierNotes(int supplierId) {
            if (supplierId < 0) return string.Empty;
            var advisoryNote = await _mediator.Send(new GetCompanyAdvisoryNoteQuery(){ Id = supplierId});
            return advisoryNote.Data ?? string.Empty;
        }
        private async Task<bool> IsQualityGroupUser(int userId, int clientId) {
            var isQualityGroupUser = await _mediator.Send(new GetIsQualityGroupUserQuery(userId, clientId));
            return isQualityGroupUser.Data;
        }
    }
}
