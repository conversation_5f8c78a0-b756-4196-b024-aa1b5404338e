using GlobalTrader2.Aggregator.UseCases.DataList.GetActiveCommunicationLogType;
using GlobalTrader2.Contacts.UseCases.Companies.Queries.Details.CheckCompanyInactive;
using GlobalTrader2.Contacts.UseCases.Companies.Queries.Details.IsReadOnly;
using GlobalTrader2.Contacts.UseCases.Contact.ContactDetails.Commands.InsertContactLog;
using GlobalTrader2.Contacts.UseCases.Contact.ContactDetails.Commands.UpdateContactLog;
using GlobalTrader2.Contacts.UseCases.Contact.ContactDetails.Queries.GetContactLog;
using GlobalTrader2.Core.Bases;
using GlobalTrader2.Core.Enums;
using GlobalTrader2.Core.Exceptions;
using GlobalTrader2.Dto.CommunicationLog;
using GlobalTrader2.Dto.Contacts;
using GlobalTrader2.Dto.Datatables;
using GlobalTrader2.SharedUI;
using GlobalTrader2.SharedUI.Authorization;
using GlobalTrader2.SharedUI.Bases;
using GlobalTrader2.SharedUI.Enums;
using GlobalTrader2.SharedUI.Services;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;


namespace GlobalTrader2.Contacts.UI.Areas.Contact.Controllers.AllCompanies
{
    [Authorize]
    [Route("api/contact/all-companies")]
    public class ContactLogController : ApiBaseController
    {
        private readonly ISender _mediator;
        private readonly SessionManager _sessionManager;
        private readonly IStringLocalizer<Misc> _miscLocalizer;

        public ContactLogController(ISender mediator, SessionManager sessionManager, IStringLocalizer<Misc> miscLocalizer)
        {
            _mediator = mediator;
            _sessionManager = sessionManager;
            _miscLocalizer = miscLocalizer;
        }

        [ApiAuthorize(false, SecurityFunction.Contact_CompanyDetail_ContactLog_Add)]
        [HttpPost("{supplierId}/contact-log")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> InsertContactLog([FromBody] CreateCommunicationLogRequestDto request, [FromRoute] int supplierId)
        {
            var isReadOnly = await _mediator.Send(new IsReadOnlyQuery()
            {
                LoginId = _sessionManager.LoginID ?? default,
                ClientId = _sessionManager.ClientID ?? default,
                CompanyId = supplierId,
                IsGlobalUser = _sessionManager.IsGlobalUser,
            });

            if (isReadOnly) return Unauthorized();

            await ValidateInactiveCompany(supplierId, "Add Contact Log", "Cannot add a Contact Log for an Inactive Company");
            request.UpdatedBy = UserId;
            request.Frozen = false;
            var result = await _mediator.Send(new InsertContactLogCommand(request));
            return Ok(result);
        }

        [ApiAuthorize(false, SecurityFunction.Contact_CompanyDetail_ContactLog_Edit)]
        [HttpPut("{supplierId}/contact-log/{contactLogId}")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateContactLog([FromBody] UpdateCommunicationRequestDto request, [FromRoute] int contactLogId, [FromRoute] int supplierId)
        {
            var isReadOnly = await _mediator.Send(new IsReadOnlyQuery()
            {
                LoginId = _sessionManager.LoginID ?? default,
                ClientId = _sessionManager.ClientID ?? default,
                CompanyId = supplierId,
                IsGlobalUser = _sessionManager.IsGlobalUser,
            });

            if (isReadOnly) return Unauthorized();

            await ValidateInactiveCompany(supplierId, "Edit Contact Log", "Cannot edit a Contact Log for an Inactive Company");
            request.CommunicationLogId = contactLogId;
            request.UpdatedBy = UserId;
            var result = await _mediator.Send(new UpdateContactLogCommand(request));
            return Ok(result);
        }

        [HttpPost("contact-log/search")]
        public async Task<IActionResult> GetContactLogs([FromBody] GetContactLogsRequest request, CancellationToken cancellationToken)
        {
            try
            {
                request.PageIndex = request.Start / request.Length;
                await ProcessFilters(request);

                var query = new GetContactLogQuery()
                {
                    ClientId = ClientId,
                    Data = request,
                };
                var result = await _mediator.Send(query);

                var response = new DatatableResponse<IEnumerable<ContactLogDto>>();
                if (result.Success && result.Data != null)
                {
                    response.Success = result.Success;
                    response.RecordsTotal = result.Data.TotalRecords;
                    response.RecordsFiltered = result.Data.TotalRecords;
                    response.Draw = request.Draw;
                    result.Data.Items.ForEach(x =>
                    {
                        if (x.SysDocNo != null)
                        {
                            x.Type = _miscLocalizer.GetString($"New{((SystemDocument.List)x.SysDocNo).ToString()}");
                            x.Frozen = true;
                        }
                    });
                    response.Data = result.Data.Items;
                }
                return Ok(response);
            }
            catch (TaskCanceledException)
            {
                HttpContext.Response.StatusCode = 499;
                return new ObjectResult(new BaseResponse<string>()
                {
                    Data = "Request was canceled"
                });
            }
        }
        private async Task ProcessFilters(GetContactLogsRequest request)
        {
            if (request.Filters != null)
            {
                var activeCommunicationLogTypes = await _mediator.Send(new GetActiveCommunicationLogTypeQuery());
                if (activeCommunicationLogTypes.Data != null)
                {
                    if (activeCommunicationLogTypes.Data.Any(c => c.CommunicationLogTypeId == request.Filters.CommunicationLogTypeNo))
                    {
                        request.Filters.LogCallType = "LOGTYPE";
                    }
                    else
                    {
                        var systemDocuments = Enum.GetValues(typeof(SystemDocument.ListForSequencer))
                                .Cast<SystemDocument.ListForSequencer>();
                        if (systemDocuments.Any(sd => (int)sd == request.Filters.CommunicationLogTypeNo))
                        {
                            request.Filters.LogCallType = "SYSDOC";
                        }
                    }
                }
            }
        }

        private async Task ValidateInactiveCompany(int supplierId, string propertyMessage, string errorMessage)
        {
            var checkCompanyInactiveQuery = new CheckCompanyInactiveQuery(supplierId);
            var companyInactiveResponse = await _mediator.Send(checkCompanyInactiveQuery);
            if (companyInactiveResponse.Success && companyInactiveResponse.Data)
            {
                throw new ValidationException(new List<Core.Bases.BaseError>() {
                    new Core.Bases.BaseError()
                    {
                        PropertyMessage = propertyMessage,
                        ErrorMessage = errorMessage
                    }
                });
            }
        }
    }
}
