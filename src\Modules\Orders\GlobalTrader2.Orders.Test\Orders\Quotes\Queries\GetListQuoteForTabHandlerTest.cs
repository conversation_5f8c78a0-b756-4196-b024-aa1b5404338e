﻿using GlobalTrader2.Dto.AutoSourcing;
using GlobalTrader2.Orders.UserCases.Orders.AutoSourcing.GetAutoSourcingHUBRFQItem;
using GlobalTrader2.Orders.UserCases.Orders.AutoSourcing.Queries.GetAutoSourcingHUBRFQItem;
using GlobalTrader2.Orders.UserCases.Orders.Quotes.Queries.GetListQuoteForTab;
using MediatR;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.Quotes.Queries
{
    public class GetListQuoteForTabHandlerTest
    {
        private readonly Mock<IBaseRepository<GlobalTrader2.Core.Domain.Entities.QuoteReadModel>> _mockRepo;
        private readonly GetListQuoteForTabHandler _handler;
        private readonly GetListQuoteForTabQuery _query;
        private readonly IFixture _fixture;

        public GetListQuoteForTabHandlerTest()
        {
            _fixture = new Fixture();
            _mockRepo = _fixture.Freeze<Mock<IBaseRepository<GlobalTrader2.Core.Domain.Entities.QuoteReadModel>>>();
            _handler = new GetListQuoteForTabHandler(_mockRepo.Object);
            _query = _fixture.Create<GetListQuoteForTabQuery>();
        }

        [Fact]
        public async Task Handle_AutoSourcingAreFound_ReturnsSuccess()
        {
            // Arrange
            var entities = _fixture.Create<IReadOnlyList<GlobalTrader2.Core.Domain.Entities.QuoteReadModel>>();

            _mockRepo.Setup(repo => repo.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
             .ReturnsAsync(entities);

            // Act
            var response = await _handler.Handle(_query, CancellationToken.None);

            //Assert
            Assert.True(response.Success);
            Assert.True(response?.Data?.Any());
        }

        [Fact]
        public async Task Handle_AutoSourcingNotFound_ReturnsSuccess()
        {
            // Arrange
            var entities = _fixture.Create<IReadOnlyList<GlobalTrader2.Core.Domain.Entities.QuoteReadModel>>();
            _mockRepo.Setup(repo => repo.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
            .ReturnsAsync((IReadOnlyList<QuoteReadModel>?)null);

            // Act
            var response = await _handler.Handle(_query, CancellationToken.None);

            //Assert
            Assert.True(response.Success);
            Assert.True(response?.Data is null);
        }
    }
}
