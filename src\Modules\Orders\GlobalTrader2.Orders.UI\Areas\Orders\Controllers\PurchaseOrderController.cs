﻿using System.Globalization;
using GlobalTrader2.Dto.DataListNugget;
using GlobalTrader2.Dto.Datatables;
using GlobalTrader2.Dto.PurchaseOrder;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetPoIdByNumber;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetPurchaseOrderDataList;
using GlobalTrader2.SharedUI.Models;
using GlobalTrader2.SharedUI.Services;
using GlobalTrader2.UserAccount.UseCases.FilterState.Commands.SaveFilterStates;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Controllers;

[ApiController]
[Authorize]
[Route("api/orders/purchase-order")]
public class PurchaseOrdersController(IMediator mediator, SessionManager sessionManager) : ApiBaseController
{
    private readonly IMediator _mediator = mediator;
    private readonly SessionManager _sessionManager = sessionManager;
    [HttpGet("search-id")]
    public async Task<IActionResult> GetPurchaseOrderIdByNumber([FromQuery] int poNum)
    {
        return Ok(await _mediator.Send(new GetPurchaseOrderIdQuery()
        {
            ClientNo = ClientId,
            PONum = poNum,
            IsGlobalUser = IsGlobalLogin
        }));
    }
    [HttpPost("list")]
    [ApiAuthorize(isDataOtherClient: false, SecurityFunction.OrdersSection_View)]
    public async Task<IActionResult> GetListPurchaseOrderAsync([FromBody] GetPurchaseOrderPagingRequest request, CancellationToken cancellationToken)
    {
        try
        {
            request.PageSize = request.Length;
            request.PageIndex = request.Start / request.Length;
            request.LoginId = request.LoginId.HasValue ? request.LoginId : UserId;
            request.TeamId = LoginTeamID;
            request.DivisionId = LoginDivisionID;
            request.ClientId = ClientId;
            request.IsGlobalUser = _sessionManager.IsGlobalUser;
            request.IsGSA = _sessionManager.IsGSA;
            var dataListStates = new DataListNuggetState<PurchaseOrderFilter>();
            dataListStates.SortIndex = request.OrderBy;
            dataListStates.SortDirection = request.SortDir;
            dataListStates.Page = request.PageIndex + 1;
            dataListStates.PageSize = request.PageSize;
            dataListStates.AddFilterStates(request.Filters);
            var PurchaseOrderFilterStates = GetFilterRequest(dataListStates.ObjFilterStates);

            if (request.SaveStates)
            {
                await _mediator.Send(new SaveFilterStatesCommand
                {
                    DataListNuggetNo = (int)DataListNuggets.PurchaseOrders,
                    LoginNo = UserId,
                    StateText = dataListStates.ToString()
                }, cancellationToken);
            }

            var result = await _mediator.Send(new GetPurchaseOrderDataListQuery { Data = request, Filters = PurchaseOrderFilterStates, CultureInfo = new CultureInfo(Culture), IsPOHub = IsPOHub }, cancellationToken);
            var totalItems = result.Data?.FirstOrDefault()?.RowCnt ?? 0;

            var response = new DatatableResponse<List<PurchaseOrderDataListDto>>()
            {
                Success = result.Success,
                Data = result.Data,
                RecordsTotal = totalItems,
                RecordsFiltered = totalItems,
                Draw = request.Draw
            };

            return Ok(response);
        }
        catch (TaskCanceledException)
        {
            HttpContext.Response.StatusCode = 499;
            return new ObjectResult(new BaseResponse<string>()
            {
                Data = "Request was canceled"
            });
        }
    }

    private static PurchaseOrderFilter GetFilterRequest(PurchaseOrderFilter? filter)
    {
        if (filter == null)
        {
            return new PurchaseOrderFilter();
        }

        filter.Part = string.IsNullOrEmpty(filter.Part) ? null : TextBoxState.FormatForLikeSearchNoPunctuation(filter.Part);
        filter.CMName = string.IsNullOrEmpty(filter.CMName) ? null : TextBoxState.FormatForLikeSearchNoPunctuation(filter.CMName);
        filter.Contact = string.IsNullOrEmpty(filter.Contact) ? null : TextBoxState.FormatForLikeSearchNoPunctuation(filter.Contact);
        return filter;
    }
}
