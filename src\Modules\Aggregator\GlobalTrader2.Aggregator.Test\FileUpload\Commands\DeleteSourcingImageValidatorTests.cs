﻿using FluentValidation.TestHelper;
using GlobalTrader2.Aggregator.UseCases.FileUpload.Commands.DeleteSourcingImage;
using GlobalTrader2.Aggregator.UseCases.FileUpload.Commands.InsertSourcingImage;
using GlobalTrader2.Orders.UserCases.Orders.Sourcing.Commands.InsertSourcingImage;

namespace GlobalTrader2.Aggregator.Test.FileUpload.Commands
{
    public class DeleteSourcingImageValidatorTests
    {
        private readonly DeleteSourcingImageValidator _validator;

        public DeleteSourcingImageValidatorTests()
        {
            _validator = new DeleteSourcingImageValidator();
        }

        [Fact]
        public async Task Should_Not_Have_Error_When_SourcingImageId_Is_Valid()
        {
            var model = new DeleteSourcingImageCommand(123);
            var result = await _validator.TestValidateAsync(model);
            result.ShouldNotHaveAnyValidationErrors();
        }
    }
}
