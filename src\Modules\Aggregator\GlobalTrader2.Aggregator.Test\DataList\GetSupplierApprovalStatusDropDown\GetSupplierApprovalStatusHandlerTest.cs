﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoFixture;
using GlobalTrader2.Aggregator.UseCases.DataList.GetPurchaseOrderStatus;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Core;
using Moq;
using GlobalTrader2.Aggregator.UseCases.DataList.GetSupplierApprovalStatusDropDown;

namespace GlobalTrader2.Aggregator.Test.DataList.GetSupplierApprovalStatusDropDown
{
    public class GetSupplierApprovalStatusHandlerTest
    {
        private readonly Mock<IBaseRepository<SupplierApprovalStatusReadModel>> _repository;
        private readonly GetSupplierApprovalStatusHandler _handler;
        private readonly IFixture _fixture = new Fixture();
        public GetSupplierApprovalStatusHandlerTest()
        {
            _repository = new Mock<IBaseRepository<SupplierApprovalStatusReadModel>>();
            _handler = new GetSupplierApprovalStatusHandler(_repository.Object);
        }
        [Fact]
        public async Task GetSupplierApprovalStatusHandler_ReturnValueTest()
        {
            var supplierApprovalStatus = _fixture.CreateMany<SupplierApprovalStatusReadModel>(5).ToList();
            _repository.Setup(repo => repo.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>(),It.IsAny<int>()))
                .ReturnsAsync(supplierApprovalStatus);
            // Act
            var result = await _handler.Handle(new GetSupplierApprovalStatusDropDownQuery(), CancellationToken.None);
            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);

        }
    }
}
