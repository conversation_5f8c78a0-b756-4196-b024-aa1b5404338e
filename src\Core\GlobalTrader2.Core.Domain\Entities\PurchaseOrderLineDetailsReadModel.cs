using System.ComponentModel.DataAnnotations.Schema;

namespace GlobalTrader2.Orders.UseCases.PurchaseOrderLine.Models
{
    public class PurchaseOrderLineDetailsReadModel
    {
        public int PurchaseOrderLineId { get; set; }
        public int? PurchaseOrderNo { get; set; }
        public string? FullPart { get; set; }
        public string? Part { get; set; }
        public int? ManufacturerNo { get; set; }
        public string? DateCode { get; set; }
        public int? PackageNo { get; set; }
        public int? Quantity { get; set; }
        public double? Price { get; set; }
        public DateTime? DeliveryDate { get; set; }
        public string? ReceivingNotes { get; set; }
        public bool? Taxable { get; set; }
        public bool? RepeatOrder { get; set; }
        public int? ProductNo { get; set; }
        public bool? Posted { get; set; }
        public double? ShipInCost { get; set; }
        public string? SupplierPart { get; set; }
        public bool? Inactive { get; set; }
        public bool? Closed { get; set; }
        public byte? ROHS { get; set; }
        public int? UpdatedBy { get; set; }
        public DateTime? DLUP { get; set; }
        public int? PurchaseOrderNumber { get; set; }
        public string? CurrencyCode { get; set; }
        public DateTime? DateOrdered { get; set; }
        public string? CompanyName { get; set; }
        public int? CompanyNo { get; set; }
        public string? ManufacturerCode { get; set; }
        public string? LineNotes { get; set; }
        public int? QuantityReceived { get; set; }
        public int? QuantityAllocated { get; set; }
        public double? GIShipInCost { get; set; }
        public string? ProductName { get; set; }
        public string? ProductDescription { get; set; }
        public string? ProductDutyCode { get; set; }
        public string? PackageName { get; set; }
        public string? PackageDescription { get; set; }
        public double? ImportCountryShippingCost { get; set; }
        public int? CurrencyNo { get; set; }
        public string? CurrencyDescription { get; set; }
        public string? ManufacturerName { get; set; }
        public double? TaxRate { get; set; }
        public int? ClientNo { get; set; }
        public int? ImportCountryNo { get; set; }
        public DateTime? PromiseDate { get; set; }
        public int? PurchaseQuoteId { get; set; }
        public int? PurchaseQuoteNumber { get; set; }
        public DateTime? PurchaseRequestDate { get; set; }
        public int? BomNo { get; set; }
        public string? BOMName { get; set; }
        public double? ClientPrice { get; set; }
        [Column("InternalPurchaseOrderId")]
        public int? InternalPurchaseOrderNo { get; set; }
        public int? ClientCurrencyNo { get; set; }
        public string? ClientCurrencyCode { get; set; }
        [Column("ClientEstShipCost")]
        public double? ClientShipInCost { get; set; }
        public int? IPOClientNo { get; set; }
        public int? DefaultClientLotNo { get; set; }
        public bool? ProductInactive { get; set; }
        [Column("ProductDutyRate")]
        public double? DutyRate { get; set; }
        public bool? ReqSerialNo { get; set; }
        [Column("MSLLevel")]
        public string? MSLevel { get; set; }
        public int? SupplierWarranty { get; set; }
        public string? EPRIds { get; set; }
        public bool? IsReleased { get; set; }
        public bool? IsAuthorised { get; set; }
        public bool? IsProdHazardous { get; set; }
        public bool? PrintHazardous { get; set; }
        [Column("IHSCountryOfOrigin")]
        public string? CountryOfOrigin { get; set; }
        public string? LifeCycleStage { get; set; }
        public string? HTSCode { get; set; }
        public double? AveragePrice { get; set; }
        [Column("Packing")]
        public string? Packaging { get; set; }
        public string? PackagingSize { get; set; }
        public string? Descriptions { get; set; }
        public string? IHSProduct { get; set; }
        public string? ECCNCode { get; set; }
        public DateTime? OriginalDeliveryDate { get; set; }
        public double? SOPrice { get; set; }
        public double? IpoLineTotal { get; set; }
        public double? LineProfit { get; set; }
        public double? LineProfitPercentage { get; set; }
        public bool? IsOrderViaIPOonly { get; set; }
        public int? HubCurrencyNo { get; set; }
        public DateTime? CurrencyDate { get; set; }
        public string? IHSECCNCodeDefination { get; set; }
        public bool? IsRestrictedProduct { get; set; }
        [Column("ECCNClientNotify")]
        public bool? ECCNNotify { get; set; }
        public string? ECCNSubject { get; set; }
        public string? ECCNMessage { get; set; }
        public bool? AS6081 { get; set; }
        public bool? IsSanctioned { get; set; }
        public int? ECCNId { get; set; }
        public string? ECCNCodeEdit { get; set; }
        public string? ECCNCodeSOLine { get; set; }
        public int? ECCNIdSOLine { get; set; }
        [Column("SalesOrderId")]
        public int? SalesOrderNo { get; set; }
        public int? SalesOrderNumber { get; set; }
        public int? SalesOrderLineNo { get; set; }
        public long? SOLineRank { get; set; }
        public long? POLineRank { get; set; }
        public int? ServiceNo { get; set; }
        public double? ServiceCost { get; set; }
        public DateTime? ServiceDateRequired { get; set; }
    }
}