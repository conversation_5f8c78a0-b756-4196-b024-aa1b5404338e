﻿import { But<PERSON><PERSON>elper } from "../../../../helper/button-helper.js?v=#{BuildVersion}#";
import { SectionBox } from "../../../../components/base/section-box.component.js?v=#{BuildVersion}#";
import BindingService from "../../../../helper/binding.service.js?v=#{BuildVersion}#";
import { PartTableButton } from "../../../../components/button/part-table-button.js?v=#{BuildVersion}#";
import { getUrl } from '../../../../helper/url-helper.js?v=#{BuildVersion}#';

const quoteStatusMap = {
    'Offered': 1,
    'Partially Offered': 6
};
export class SourcingResults {
    constructor(selector, containerId, tableId, resizeId, customerInfo, isPoHub, isAssignToMe, options) {
        this.state = {
            boxId: selector,
            containerId: containerId,
            tableId: tableId,
            resizeId: resizeId,
            options: options ?? {},
            sourcingResultsTable: null,
            sourcingResultsSectionBox: null,
            customerInfo: {
                currentSelectedPartDetailItem: null,
                api: customerInfo.api
            },
            isPoHub: isPoHub,
            isAssignToMe: isAssignToMe,
            sourcingResultDetail: null,
            selectedSourcingResultDetailsId: null,
            selectedRow: null,
            addSourcingResultQuoteBtn: null,
            selectedRowsSourcingResult: null,
            rowDataHubrfqItemsTable: null
        }
        window.redirectToTaskDetails = this.redirectToTaskDetails.bind(this);
    }

    init() {
        this.setupSourcingResultSelectAllButton();

        this.state.sourcingResultDetail = $('#sourcing-result-detail');

        this.state.sourcingResultsSectionBox = new SectionBox(`#${this.state.boxId}`, {
            loadingContentId: "customer-requirement-sourcing-results-table-container"
        });

        this.state.sourcingResultsSectionBox.on('onRefreshed.msb', async () => {
            if (this.state.selectedRow) {
                window.lastSelectedRow = this.state.selectedRow[0];
            }
            this.toggleSourcingResultsResizeBar(false);
            this.state.sourcingResultDetail.addClass("d-none");
            await this.loadSourcingResultsAsync(this.state.customerInfo.currentSelectedPartDetailItem, true);
        });

        this.state.isClosed = false;
        this.state.sourcingResultsSectionBox.init();

        if (!this.state.customerInfo.currentSelectedPartDetailItem) {
            $(`#${this.state.boxId}`).addClass('d-none');
        }
        this.state.addSourcingResultQuoteBtn = new PartTableButton('#addSourcingResultQuote', () => {
            if (this.state.selectedRowsSourcingResult.some(item => !item.mslLevelNo)) {
                BindingService.bindToElements("#part-detail-sourcing-results-header-message", {
                    error: localizedTitles.kindlyUpdateMsl
                })
                $("#part-detail-sourcing-results-header-message").show();
                return;
            }

            //Get to URL Quote Add
            const selectedLineIDs = GlobalTrader.StringHelper.arrayToSingleString(this.state.selectedRowsSourcingResult.map(row => row.id))
            location.href = getUrl(ButtonHelper.URL_Quote_Add(this.state.rowDataHubrfqItemsTable[0].companyNo, null, this.state.rowDataHubrfqItemsTable[0].customerRequirementId, selectedLineIDs));
        });

        this.state.addSourcingResultQuoteBtn.disable();

        this.setupDeleteButton();
        this.setupDeletePartWatchButton();
        this.setupReleaseButton();
        $('#customer-requrement-sourcing-results-box .ui-accordion-header').on('click', function () {
            this.setSourcingResultsTableHeight(4);
        }.bind(this));
        $('#invalid-price-box').addClass("d-none");
    }

    async setSelectedCustomerRequirementId(customerRequirementId) {
        if (customerRequirementId !== this.state.customerInfo.currentSelectedPartDetailItem) {
            this.state.customerInfo.currentSelectedPartDetailItem = customerRequirementId;
            $(`#${this.state.boxId}`).removeClass('d-none');
            this.state.sourcingResultDetail.addClass("d-none");
            await this.loadSourcingResultsAsync(this.state.customerInfo.currentSelectedPartDetailItem);
        }
    }

    setIsClose(closed) {
        this.state.isClosed = closed;
    }

    async setIsAssignToMe(isAssignToMe) {
        if (isAssignToMe) {
            this.state.isAssignToMe = isAssignToMe;
        }
    }

    async setupSourcingResultsTable() {
        let requirementId = this.state.customerInfo.currentSelectedPartDetailItem;
        let tableSelector = `#${this.state.tableId}`;

        if ($.fn.DataTable.isDataTable(tableSelector)) {
            const $table = $(tableSelector);
            $table.DataTable().destroy();
            $table.empty();
        }

        let isPOHub = this.state.isPoHub;
        this.state.sourcingResultsTable = new DataTable(`#${this.state.tableId}`, {
            paging: false,
            searching: false,
            ordering: false,
            autoWidth: false,
            scrollCollapse: true,
            loading: true,
            info: false,
            select: {
                toggleable: true,
                info: false,
            },
            columnDefs: [
                { type: 'string', targets: '_all' }
            ],
            language: {
                emptyTable: localizedTitles.sourcingResultsEmptyMsg,
                zeroRecords: localizedTitles.sourcingResultsEmptyMsg,
            },
            columns: [
                {
                    ...this.createDataTableDefaultColumns('supplier', localizedTitles.supplier, localizedTitles.relatedQuotes),
                    width: isPOHub ? "14%" : "18%",
                    render: function (_data, _type, row) {
                        let strQuotes = "";
                        if (row.quotes.length > 0) {
                            if (isPOHub) {
                                const quoteNos = row.quotes.map(q => q.no)
                                strQuotes = quoteNos.join(' ');
                            }
                            else {
                                row.quotes.forEach(quote => {
                                    strQuotes += ButtonHelper.nubButton_Quote(quote.id, quote.no);
                                });
                            }
                        }
                        let tooltip = '';
                        if (row.mslSpqFactorySealed) {
                            tooltip = row.mslSpqFactorySealed;
                        }

                        let supplierInfo;
                        if (row.supplier) {
                            let supplierHtml = ButtonHelper.nubButton_Company(row.supplierNo, row.supplier, row.supplierAdvisoryNotes, null, null);
                            supplierInfo = supplierHtml ? `<span title="${tooltip}">${GlobalTrader.StringHelper.setCleanTextValue(supplierHtml)}</span>` : '';
                        }

                        if (isPOHub) {
                            return `${(supplierInfo?.trim() ? supplierInfo + '&nbsp;&nbsp;' : '')}(${row.supplierPercentage}%) <br/>
                                    ${GlobalTrader.StringHelper.setCleanTextValue(row.supplierType)} <br/>
                                    ${GlobalTrader.StringHelper.setCleanTextValue(strQuotes)}`;
                        }

                        return `${supplierInfo} <br/>
                                ${GlobalTrader.StringHelper.setCleanTextValue(row.supplierType)} <br/>
                                ${GlobalTrader.StringHelper.setCleanTextValue(strQuotes)}`;
                    }
                },
                {
                    ...this.createDataTableDefaultColumns('part', localizedTitles.partNo, localizedTitles.partNotes),
                    width: isPOHub ? "10%" : "14%",
                    render: function (_data, _type, row) {
                        return `${GlobalTrader.StringHelper.setCleanTextValue(row.part)}<br/><span class=${isPOHub ? "" : "is-hub"}>${GlobalTrader.StringHelper.setCleanTextValue(row.notes)}</span>`;
                    }
                },
                {
                    ...this.createDataTableDefaultColumns('mfrCode', localizedTitles.mfr, localizedTitles.dC),
                    width: "6%",
                    render: function (_data, _type, row) {
                        return `${row.mfrCode ? (GlobalTrader.StringHelper.setCleanTextValue(ButtonHelper.nubButton_Manufacturer(row.mfrNo, row.mfrCode, row.mfrAdvisoryNotes)) + "<br>") : ""} ${GlobalTrader.StringHelper.setCleanTextValue(row.supplierDateCode)}`;
                    }
                },
                {
                    ...this.createDataTableDefaultColumns('product', localizedTitles.product, localizedTitles.package),
                    width: isPOHub ? "10%" : "14%",
                    render: function (_data, _type, row) {
                        return `${GlobalTrader.StringHelper.setCleanTextValue(row.product)} <br> ${GlobalTrader.StringHelper.setCleanTextValue(row.package)}`;
                    }
                },
                {
                    ...this.createDataTableDefaultColumns('offeredDate', localizedTitles.offered, localizedTitles.offeredBy),
                    width: "6%",
                    render: function (_data, _type, row) {
                        return `${GlobalTrader.StringHelper.setCleanTextValue(row.offeredDate)} <br> ${GlobalTrader.StringHelper.setCleanTextValue(row.offeredBy)}`;
                    }
                },
                {
                    ...this.createDataTableDefaultColumns('quantity', localizedTitles.quantity, localizedTitles.deliveryDate),
                    render: function (_data, _type, row) {
                        if (row.quantity == '0')
                            return '';
                        return `${GlobalTrader.StringHelper.setCleanTextValue(row.quantity)} <br> ${GlobalTrader.StringHelper.setCleanTextValue(row.deliveryDate)}`;
                    }
                },
                {
                    ...this.createDataTableDefaultColumns('buyPrice', localizedTitles.buyPrice, localizedTitles.buyPriceInBase),
                    visible: isPOHub,
                    render: function (_data, _type, row) {
                        return `${GlobalTrader.StringHelper.setCleanTextValue(row.buyPrice)} <br> ${GlobalTrader.StringHelper.setCleanTextValue(row.actualPrice)}`;
                    }
                },
                {
                    ...this.createDataTableDefaultColumns('price', isPOHub ? localizedTitles.unitSellPrice : localizedTitles.unitPrice, localizedTitles.baseCurrency),
                    render: function (_data, _type, row) {
                        return `${GlobalTrader.StringHelper.setCleanTextValue(row.price)} <br> ${GlobalTrader.StringHelper.setCleanTextValue(row.priceInBase)}`;
                    }
                },
                {
                    ...this.createDataTableDefaultColumns('pfpq', localizedTitles.priceRequest, localizedTitles.region),
                    visible: isPOHub,
                    render: function (_data, _type, row) {
                        return `${GlobalTrader.StringHelper.setCleanTextValue(row.pfpq)} <br> ${GlobalTrader.StringHelper.setCleanTextValue(row.regionName)}`;
                    }
                },
                {
                    ...this.createDataTableDefaultColumns('regionName', localizedTitles.region, localizedTitles.terms),
                    visible: !isPOHub,
                    render: function (_data, _type, row) {
                        return `${GlobalTrader.StringHelper.setCleanTextValue(row.regionName)} <br> ${GlobalTrader.StringHelper.setCleanTextValue(row.termsName)}`;
                    }
                },
                {
                    ...this.createDataTableDefaultColumns('estimatedShippingCost', localizedTitles.estShippingCost, localizedTitles.baseCurrency),
                    render: function (_data, _type, row) {
                        return `${GlobalTrader.StringHelper.setCleanTextValue(row.estimatedShippingCost)} <br> ${GlobalTrader.StringHelper.setCleanTextValue(row.estimatedShippingCostInBase)}`;
                    }
                },
                {
                    ...this.createDataTableDefaultColumns('partWatchMatchHUBIPO', localizedTitles.partWatchMatch, localizedTitles.sourceClient),
                    visible: isPOHub,
                    render: function (_data, _type, row) {
                        const valueString = row.partWatchMatchHUBIPO ? "<input type='checkbox' class='form-check-input check-sm' checked onclick='return false'/>" : "<input type='checkbox' class='form-check-input check-sm' onclick='return false'/>";
                        return `${GlobalTrader.StringHelper.setCleanTextValue(valueString)} <br> ${GlobalTrader.StringHelper.setCleanTextValue(row.sourceClient)}`;
                    }
                },
                {
                    ...this.createDataTableDefaultColumns(null, localizedTitles.toDoList),
                    className: "text-break align-baseline position-relative",
                    render: function (_data, _type, row) {
                        let addTaskHtml = "";
                        let viewTaskHtml = "";
                        if (row.quotes.length > 0) {
                            const quote = row.quotes[0];

                            let encodedCompanyName = GlobalTrader.StringHelper.htmlAttributeEncode(quote.customerName);
                            let quoteStatusCode = quoteStatusMap[quote.quoteStatus] || 0;
                            addTaskHtml = `<a class="dt-hyper-link" 
                                        data-company-name="${encodedCompanyName}" 
                                        data-category-type="${QUOTE_TASK_CATEGORY}" 
                                        data-quote-number="${quote.no}" 
                                        data-quote-id="${quote.id}" 
                                        data-quote-status="${quoteStatusCode}"
                                        data-requirement-id="${requirementId}"
                                        onClick="event.stopPropagation(); openSpecificCategoryToDoDialog(event)"
                                        title='Add task'>Add Task</a>`;
                            if (quote.hasUnFinishedTask) {
                                viewTaskHtml = `<a class="dt-hyper-link view-task" href="javascript:void(0);" title="Need to complete open Tasks" onclick="event.stopPropagation(); redirectToTaskDetails('${quote.no}')">${quote.taskCount} Task</a>`;
                            }
                            else {
                                viewTaskHtml = `<a class="dt-hyper-link" href="javascript:void(0);" title="View task" onclick="event.stopPropagation(); redirectToTaskDetails('${quote.no}')">${quote.taskCount} Task</a>`;
                            }
                        }

                        return `${GlobalTrader.StringHelper.setCleanTextValue(addTaskHtml)} <br> ${GlobalTrader.StringHelper.setCleanTextValue(viewTaskHtml)}`;
                    }
                }
            ],
            createdRow: (row, data) => { },
            rowCallback: function (row, data) {
                if (data.partWatchMatch) {
                    $(row).addClass('part-watch-match');
                }
            },
            rowId: 'id',
        });

        this.state.sourcingResultsTable.on("select deselect", async (e, dt, type, indexes) => {
            if (type === 'row') {
                $("#part-detail-sourcing-results-header-message").hide();
                this.state.selectedRowsSourcingResult = this.state.sourcingResultsTable.rows({ selected: true }).data().toArray();
                const selectedCount = this.state.sourcingResultsTable.rows({ selected: true }).count();
                this.state.rowDataHubrfqItemsTable = $('#hub-rfq-items-table').DataTable().rows({ selected: true }).data().toArray();
                const isCusClosed = this.state.rowDataHubrfqItemsTable.length > 0 && this.state.rowDataHubrfqItemsTable[0].closed;
                this.appendSelectAllButton(selectedCount);
                this.handleShowDeletePartWatchButton(this.state.selectedRowsSourcingResult);
                this.handleShowDeleteButton(this.state.selectedRowsSourcingResult);
                this.handleShowReleaseButton(this.state.selectedRowsSourcingResult);
                this.handleShowEditButton(this.state.selectedRowsSourcingResult);

                const row = this.state.selectedRowsSourcingResult[0];
                const isSourcingReleased = this.state.selectedRowsSourcingResult.length > 0 ? !!row.sourcRelease : true;
                const canEditSourcingResult = this.state.isAssignToMe && !isCusClosed && !isSourcingReleased && !this.state.isPoHub;
                if (selectedCount == 1) {
                    $("#editSourcingResult").attr("disabled", !canEditSourcingResult);
                    this.state.selectedSourcingResultDetailsId = row.id;
                    this.handleShowItemDetail(row);
                } else {
                    $("#editSourcingResult").attr("disabled", true);
                    this.handleShowItemDetail();
                    this.state.sourcingResultDetail.addClass("d-none");
                }
                const allValid = !isCusClosed && selectedCount > 0 && !this.state.isPoHub && this.state.selectedRowsSourcingResult.every(r =>
                    !r.isSoCreated &&
                    !r.isClosed
                );
                if (allValid) {
                    $("#addSourcingResultQuote").attr("disabled", false);
                } else {
                    $("#addSourcingResultQuote").attr("disabled", true);
                }
            }
        });
    }

    getQuoteStatusCode(quoteStatus) {
        const quoteStatusMap = {
            'Offered': 1,
            'Partially Offered': 6
        };
        return quoteStatusMap[quoteStatus] || 0;
    }

    appendSelectAllButton(selectedCount) {
        const innerHtml = `<span class="badge bg-white text-primary top-0">${selectedCount}</span> ${selectedCount ? 'Clear All ' : 'Select All'}`;
        this.state.sourcingResultsSelectAllButton.innerHTML = innerHtml;
    }

    refreshDetail() {
        $('#date-code').html('');
        $('#package-type').html('');
        $('#product-type').html('');
        $('#supplier-warranty').html('');
        $('#testing-recommended').html('');
        $('#priority').html('');
        $('#country-of-origin').html('');
        $('#moq').html('');
        $('#total-qsa').html('');
        $('#ltb').html('');
        $('#notes').html('');
        $('#images-attached').html('');
        $('#spq').html('');
        $('#lead-time').html('');
        $('#rohs-status').html('');
        $('#factory-sealed').html('');
        $('#msl').html('');
        $('#type-of-supplier').html('');
        $('#reason-supplier').html('');
        $('#risk-supplier').html('');
    }

    handleShowItemDetail(rowData) {
        if (!rowData) {
            return;
        }

        this.refreshDetail();

        $("#testing-recommended-bg").removeClass('bg-warning');
        if (rowData.supplierManufacturerName != undefined && rowData.supplierManufacturerName != '') {
            $('#manufacturer-name').html(GlobalTrader.StringHelper.setCleanTextValue(ButtonHelper.nubButton_Manufacturer(null, rowData.supplierManufacturerName, rowData.mfrAdvisoryNotes)));
        }
        else {
            $('#manufacturer-name').html('');
        }
        $('#date-code').html(rowData.supplierDateCode);
        $('#package-type').html(rowData.supplierPackageType);
        $('#product-type').html(rowData.supplierProductType);
        $('#supplier-warranty').html(rowData.supplierWarranty > 0 ? rowData.supplierWarranty + " " + localizedSourcingResultDetails.days : "");
        $('#testing-recommended').html(rowData.isTestingRecommended ? localizedSourcingResultDetails.yes : localizedSourcingResultDetails.no);
        $('#priority').html(rowData.priorityId);
        $('#country-of-origin').html(rowData.countryOfOriginName);
        $('#moq').html(rowData.supplierMOQ);
        $('#total-qsa').html(rowData.supplierTotalQSA);
        $('#ltb').html(rowData.supplierLTB);
        $('#notes').html(rowData.notes);
        $('#images-attached').html(rowData.isImageAvail ? localizedSourcingResultDetails.yes : localizedSourcingResultDetails.no);
        $('#spq').html(rowData.spq);
        $('#lead-time').html(rowData.leadTime);
        $('#rohs-status').html(rowData.rohsDescription);
        $('#factory-sealed').html(rowData.factorySealed);
        $('#msl').html(rowData.mslLevelText);
        $('#type-of-supplier').html(rowData.typeOfSupplier);
        $('#reason-supplier').html(rowData.reasonForSupplier);
        $('#risk-supplier').html(rowData.riskOfSupplier);

        if (rowData.isTestingRecommended) {
            $("#testing-recommended-bg").addClass('bg-warning');
        }

        this.state.sourcingResultDetail.removeClass('d-none');
    }

    handleShowDeleteButton(selectedRows) {
        this.state.selectedRow = selectedRows.map(obj => obj.id);
        if (!Array.isArray(selectedRows) || selectedRows.length === 0) {
            $("#deleteSourcingResult").attr("disabled", true);
            return;
        }

        let isAssignToMeState = this.state.isAssignToMe;

        const allRowsValid = selectedRows.every(rowData => {
            if (!rowData) return false;

            let isAssignToMe = isAssignToMeState ?? rowData.isAssignToMe;

            return !rowData.sourcRelease && !rowData.partWatchMatchHUBIPO === true && isAssignToMe && rowData.sourceRef;
        });
        $("#deleteSourcingResult").attr("disabled", !allRowsValid);
    }

    handleShowDeletePartWatchButton(selectedRows) {
        this.state.selectedRow = selectedRows.map(obj => obj.id);
        if (!Array.isArray(selectedRows) || selectedRows.length === 0) {
            $("#deleteSourcingResultPartWatch").attr("disabled", true);
            return;
        }

        let isAssignToMeState = this.state.isAssignToMe;

        const allRowsValid = selectedRows.every(rowData => {
            if (!rowData) return false;

            let isAssignToMe = isAssignToMeState ?? rowData.isAssignToMe;
            return !rowData.sourcRelease && rowData.partWatchMatchHUBIPO === true && isAssignToMe;
        });
        $("#deleteSourcingResultPartWatch").attr("disabled", !allRowsValid);
    }

    handleShowReleaseButton(selectedRows) {
        this.state.selectedRow = selectedRows.map(obj => obj.id);
        if (!Array.isArray(selectedRows) || selectedRows.length === 0) {
            const releaseButton = $("#releaseSourcingResult");
            if (releaseButton.length) {
                releaseButton.attr("disabled", true);
            }
            return;
        }

        let isAssignToMeState = this.state.isAssignToMe;

        const allRowsValid = selectedRows.every(rowData => {
            if (!rowData) return false;
            let isAssignToMe = isAssignToMeState ?? rowData.isAssignToMe;
            return !rowData.sourcRelease && rowData.isSourcingReleasedCount === true && isAssignToMe;
        });

        const releaseButton = $("#releaseSourcingResult");
        if (releaseButton.length) {
            releaseButton.attr("disabled", !allRowsValid);
        }
    }

    handleShowAddButton() {
        const addButton = $("#addSourcingResultQuoteToClient");

        let isAssignToMeState = this.state.isAssignToMe;

        if (isAssignToMeState && !this.state.isClosed) {
            addButton.attr("disabled", false);
            return;
        }
        else {
            addButton.attr("disabled", true);
        }
    }

    handleShowEditButton(selectedRows) {
        const editButton = $("#editSourcingResultQuoteToClient");

        let isAssignToMeState = this.state.isAssignToMe;

        if (selectedRows.length == 1 && !selectedRows[0].sourcRelease && isAssignToMeState && !this.state.isClosed) {
            editButton.attr("disabled", false);
            return;
        }
        else {
            editButton.attr("disabled", true);
        }
    }

    setupSourcingResultSelectAllButton() {
        this.state.sourcingResultsSelectAllButton = document.getElementById('selectAllSourcingResultsButton');

        this.state.sourcingResultsSelectAllButton.addEventListener('click', () => {
            const isSelectedAnyRow = this.state.sourcingResultsTable.rows({ selected: true }).count() !== 0;
            if (isSelectedAnyRow) {
                this.state.sourcingResultsTable.rows().deselect();
                $('#sourcing-result-detail').addClass("d-none");
            }
            else {
                this.state.sourcingResultsTable.rows().select();
            }
        });
    }

    reSetupAfterLoadData() {
        $("#deleteSourcingResultPartWatch").attr("disabled", true);
        $("#deleteSourcingResult").attr("disabled", true);
        const releaseButton = $("#releaseSourcingResult");
        if (releaseButton.length) {
            releaseButton.attr("disabled", true);
        }

        $("#part-detail-sourcing-results-header-message").hide();
        this.state.sourcingResultsTable.rows().deselect();
        this.appendSelectAllButton(0);
    }

    async loadSourcingResultsAsync(selectedPart, isRefresh) {
        $("#part-detail-sourcing-results-header-message").hide();
        if (this.state.selectedRow) {
            window.lastSelectedRow = this.state.selectedRow[0];
        }
        this.toggleSourcingResultsResizeBar(false);
        this.state.sourcingResultsSectionBox.loading(true);
        const container = $("#customer-requirement-sourcing-results-table-container");
        const apiEndpoint = `${this.state.customerInfo.api}${selectedPart}/sourcing-results`;
        let sourcingResults = await GlobalTrader.ApiClient.getAsync(apiEndpoint);
        this.state.sourcingResultsTable.rows().deselect();
        this.state.sourcingResultsTable.rows().remove().draw();
        if (sourcingResults?.data?.length > 0) {
            for (let sourcingResult of sourcingResults.data) {
                this.state.sourcingResultsTable.row.add(sourcingResult).draw();
            }
            this.state.sourcingResultsSectionBox.stopLoading(false);
            this.toggleSourcingResultsResizeBar(true);
            setTimeout(() => {
                this.setSourcingResultsTableHeight(4);
                if (isRefresh) {
                    this.state.sourcingResultsTable.row(`#${window.lastSelectedRow}`).select();
                    GlobalTrader.Helper.scrollToRowContainer(container, this.state.sourcingResultsTable, window.lastSelectedRow);
                    window.lastSelectedRow = null;
                }
            }, 200);
        } else {
            this.toggleSourcingResultsResizeBar(true);
            this.state.sourcingResultsSectionBox.stopLoading(false);
            setTimeout(() => {
                this.setSourcingResultsTableHeight(1);
            }, 200);
        }
        this.state.sourcingResultDetail.addClass("d-none");
        this.reSetupAfterLoadData();
        this.handleShowAddButton();
    }

    createDataTableDefaultColumns(name, ...title) {
        return {
            title: this.renderTitle(...title),
            className: 'text-wrap text-break header-custom',
            data: name,
            name: name,
        }
    }

    renderTitle(...title) {
        if (title.length < 1)
            return '';
        if (title.length == 1)
            return title[0];
        return GlobalTrader.StringHelper.stringFormat(`<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">{0}</div>{1}`, ...title);
    }

    toggleSourcingResultsResizeBar(isVisible) {
        const $resize = $(`#${this.state.resizeId}`);
        if (isVisible) {
            $resize.removeClass("d-none");
        } else {
            $resize.addClass("d-none");
        }
    }

    setSourcingResultsTableHeight(rowCount) {
        const $container = $(`#${this.state.containerId}`);
        const currentHeight = $container.outerHeight(true);
        const targetHeight = calculateFirstXRowsHeight(this.state.tableId, rowCount);

        const newHeight = (rowCount === 1)
            ? targetHeight
            : Math.max(targetHeight, currentHeight);

        $container.css("height", `${newHeight}px`);
    }

    redirectToTaskDetails(quoteNumber) {
        const baseUrl = window.location.origin;
        const targetUrl = `${baseUrl}/Profile/ToDo?qn=${quoteNumber}&Category=2`;
        window.location.href = targetUrl;
    }

    setupDeleteButton() {
        this.state.deleteButton = document.getElementById('deleteSourcingResult');

        this.state.deleteButton.addEventListener('click', () => {
            const deleteSourcingResultDialog = $("#delete-sourcing-result-dialog").dialog();
            deleteSourcingResultDialog.dialog('option', 'width', 500);
            deleteSourcingResultDialog.dialog('open');
            const currentDialog = deleteSourcingResultDialog.dialog("instance");
            currentDialog.setLoading(true);
            $("#sourcing-result-id").val(this.state.selectedRow.join('||'));
            $("#requirement-id").val(this.state.customerInfo.currentSelectedPartDetailItem);
            currentDialog.setLoading(false);
        });
    }

    setupDeletePartWatchButton() {
        this.state.deletePartWatchButton = document.getElementById('deleteSourcingResultPartWatch');

        this.state.deletePartWatchButton.addEventListener('click', () => {
            const deleteHUBPartWatchDialog = $("#delete-sourcing-result-part-watch-dialog").dialog();
            deleteHUBPartWatchDialog.dialog('option', 'width', 500);
            deleteHUBPartWatchDialog.dialog('open');
            const currentDialog = deleteHUBPartWatchDialog.dialog("instance");
            currentDialog.setLoading(true);
            $("#sourcing-result-id").val(this.state.selectedRow.join('||'));
            $("#requirement-id").val(this.state.customerInfo.currentSelectedPartDetailItem);
            currentDialog.setLoading(false);
        });
    }

    setupReleaseButton() {
        this.state.releaseButton = document.getElementById('releaseSourcingResult');

        if (this.state.releaseButton) {
            this.state.releaseButton.addEventListener('click', () => {
                const selectedRows = this.state.sourcingResultsTable.rows({ selected: true }).data().toArray();
                if (selectedRows.some(row => this.isNullOrEmpty(row.product) || this.isNullOrEmpty(row.deliveryDate))) {
                    BindingService.bindToElements("#part-detail-sourcing-results-header-message", {
                        error: localizedErrorMessage.deliveryDateAndProduct
                    })
                    $("#part-detail-sourcing-results-header-message").show();
                    return;
                }
                $("#part-detail-sourcing-results-header-message").hide();
                $('#invalid-price-box').addClass("d-none");
                const invalidPriceRows = selectedRows
                    .filter(row => row.unitSellPrice <= row.unitBuyPrice)
                    .map(row => {
                        let message = '';
                        if (row.unitBuyPrice === row.unitSellPrice) {
                            message = `The buy price (${row.buyPrice}) of selected part is equal to the sell price (${row.price})`;
                        } else if (row.unitBuyPrice > row.unitSellPrice) {
                            message = `The buy price (${row.buyPrice}) of selected part is greater than the sell price (${row.price})`;
                        }
                        return { ...row, message };
                    });

                if (invalidPriceRows.length > 0) {
                    this.showInvalidPriceRowsTable(invalidPriceRows);
                }

                const deleteHUBPartWatchDialog = $("#release-sourcing-result-dialog").dialog();
                invalidPriceRows.length > 0 ? deleteHUBPartWatchDialog.dialog('option', 'width', 1100) : deleteHUBPartWatchDialog.dialog('option', 'width', 450);
                deleteHUBPartWatchDialog.dialog('open');
                const currentDialog = deleteHUBPartWatchDialog.dialog("instance");
                currentDialog.setLoading(true);
                $("#sourcing-result-id").val(this.state.selectedRow.join('||'));
                $("#requirement-id").val(this.state.customerInfo.currentSelectedPartDetailItem);
                currentDialog.setLoading(false);
            });
        }
    }

    isNullOrEmpty(value) {
        return value == null || value === '';
    }

    setInvalidPriceTableHeight(height = null) {
        const $container = $("#invalid-price-table-container");

        if (height !== null) {
            $container.css("height", `${height}px`);
            return;
        }

        const currentHeight = $container.outerHeight(true);
        const requiredHeight = calculateFirstXRowsHeight("invalid-price-table", 10);

        $container.css("height", `${Math.max(requiredHeight, currentHeight)}px`);
    }

    showInvalidPriceRowsTable(invalidPriceRows) {
        $('#invalid-price-box').removeClass("d-none");
        if (!$.fn.DataTable.isDataTable('#invalid-price-table')) {
            const self = this;
            $('#invalid-price-table').DataTable({
                data: invalidPriceRows,
                columns: [
                    {
                        ...this.createDataTableDefaultColumns('supplier', localizedTitles.supplier, localizedTitles.partNo),
                        render: function (_data, _type, row) {
                            return `${GlobalTrader.StringHelper.setCleanTextValue(row.supplier)} <br> ${GlobalTrader.StringHelper.setCleanTextValue(row.part)}`;
                        }
                    },
                    {
                        ...this.createDataTableDefaultColumns(null, localizedTitles.buyPrice),
                        className: "text-break align-baseline position-relative",
                        render: function (_data, _type, row) {
                            return `${GlobalTrader.StringHelper.setCleanTextValue(row.buyPrice)}`;
                        }
                    },
                    {
                        ...this.createDataTableDefaultColumns(null, localizedTitles.unitSellPrice),
                        className: "text-break align-baseline position-relative",
                        render: function (_data, _type, row) {
                            return `${GlobalTrader.StringHelper.setCleanTextValue(row.price)}`;
                        }
                    },
                    {
                        ...this.createDataTableDefaultColumns(null, localizedTitles.warningMessage),
                        className: "text-break align-baseline position-relative",
                        render: function (_data, _type, row) {
                            return `${GlobalTrader.StringHelper.setCleanTextValue(row.message)}`;
                        }
                    },
                ],
                drawCallback: function (settings) {
                    setTimeout(() => {
                        self.setInvalidPriceTableHeight();
                    }, 300);
                },
                paging: false,
                searching: false,
                info: false,
                ordering: false,
                autoWidth: false,
            });
        } else {
            const table = $('#invalid-price-table').DataTable();
            table.clear();
            table.rows.add(invalidPriceRows);
            table.draw();
        }
        $('#invalid-price-table').removeClass('hidden');
    }
}