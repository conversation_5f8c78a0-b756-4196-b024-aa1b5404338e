﻿@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer
@inject IViewLocalizer _localizer

@using GlobalTrader2.SharedUI.Enums
@{
    var sectionBoxClasses = HtmlHelperExtensions.GetSectionBoxClass();
    var headerClasses = HtmlHelperExtensions.GetHeaderClass();
    var contentClasses = HtmlHelperExtensions.GetContentClass();
}

<div id="supplier-approval-box" class="@sectionBoxClasses mb-3">
    <h3 class="@headerClasses">
        <span class="section-box-title">
            @_localizer["Supplier approvals"]
        </span>
        <span class="section-box-button-group">
            
        </span>
    </h3>
    <div class="@contentClasses" style="display: none">
        <div id="supplier-approval-wrapper">
            <div class="d-flex justify-content-between align-items-start border-bottom">
                <div id="nav-tabs-wrapper" role="tablist">
                    <div class="container p-0">
                        <div id="supplier-erai-reported-message" class="row mb-2 d-none">
                            <div class="col-auto">
                                <div class="text-white bg-red p-1 fw-bold border text-uppercase">
                                    @_messageLocalizer["SupplierERAIReported"]
                                </div>
                            </div>
                        </div>
                        <div id="product-erai-reported-message" class="row mb-2 d-none">
                            <div class="col-auto">
                                <div class="text-white bg-red p-1 fw-bold border text-uppercase">
                                    @_messageLocalizer["PartIsERAIReported"]
                                </div>
                            </div>
                        </div>
                    </div>
                    <ul class="nav nav-tabs border-0 justify-content-start" id="supplier-approval-tabs">
                        <li class="nav-item">
                            <button id="supplier-approval-approval-status-tab" class="nav-link active" data-bs-toggle="tab"
                                data-bs-target="#supplier-approval-approval-status" type="button" role="tab" aria-controls="approval-status"
                                aria-selected="true" data-view-level="@((int)SupplierApprovalTab.ApprovalStatus)">
                                @_commonLocalizer["Approval Status"]
                            </button>
                        </li>
                        <li class="nav-item">
                            <button id="supplier-approval-supplier-information-tab" class="nav-link" data-bs-toggle="tab"
                                data-bs-target="#supplier-approval-supplier-information" type="button" role="tab" aria-controls="supplier-information"
                                aria-selected="false" data-view-level="@((int)SupplierApprovalTab.SupplierInformation)">
                                @_commonLocalizer["Supplier Information"]
                            </button>
                        </li>
                        <li class="nav-item">
                            <button id="supplier-approval-supplier-approval-history-tab" class="nav-link" data-bs-toggle="tab"
                                data-bs-target="#supplier-approval-supplier-approval-history" type="button" role="tab" aria-controls="supplier-approval-history"
                                aria-selected="false" data-view-level="@((int)SupplierApprovalTab.SupplierApprovalHistory)">
                                @_commonLocalizer["Supplier Approval History"]
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="tab-content">
                <div class="tab-pane fade active show" id="supplier-approval-approval-status" role="tabpanel" aria-labelledby="supplier-approval-approval-status-tab">
                    <div class="table-container">
                        <div id="supplier-approval-approval-status-wrapper" class="position-relative">
                            <table id="supplier-approval-approval-status-table" class="table simple-table display responsive nowrap">
                                <tr>
                                    <th></th>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="supplier-approval-supplier-information" role="tabpanel" aria-labelledby="supplier-approval-supplier-information-tab">
                    <div class="table-container">
                        <div id="supplier-approval-supplier-information-wrapper" class="position-relative">
                            <table id="supplier-approval-supplier-information-table" class="table simple-table display responsive nowrap">
                                <tr>
                                    <th></th>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="supplier-approval-supplier-approval-history" role="tabpanel" aria-labelledby="supplier-approval-supplier-approval-history-tab">
                    <div class="table-container">
                        <div id="supplier-approval-supplier-approval-history-wrapper" class="position-relative">
                            <table id="supplier-approval-supplier-approval-history-table" class="table simple-table display responsive nowrap">
                                <tr>
                                    <th></th>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
