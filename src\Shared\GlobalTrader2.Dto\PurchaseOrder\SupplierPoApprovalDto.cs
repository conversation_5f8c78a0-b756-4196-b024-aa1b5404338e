﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Dto.PurchaseOrder
{
    public class SupplierPoApprovalDto
    {
        public int SupplierApprovalId { get; set; }
        public int SupplierId { get; set; }
        public string? SupplierName { get; set; }
        public int ApprovedOrdersCount { get; set; }
        public int SupplierRMAsCount { get; set; }
        public string? PurchasingMethod { get; set; }
        public string? QualityApproval { get; set; }
        public string? QualityApprovedBy { get; set; }
        public string? LineManagerApproval { get; set; }
        public string? LineManagerApprovedBy { get; set; }
        public bool SupplierERAIReported { get; set; }
        public bool PartIsERAIReported { get; set; }
        public bool? IsQualityApproved { get; set; }
        public bool? IsLineManagerApproved { get; set; }
        public string? QualityApproveDate { get; set; }
        public string? LineManagerApproveDate { get; set; }
        public bool IsEscalate { get; set; }
        public string? PartNo { get; set; }
        public int Quantity { get; set; }
        public string? PaymentTerms { get; set; }
        public string? Incoterms { get; set; }
        public string? ShipVia { get; set; }
        public string? ShipFromCountry { get; set; }
        public string? TotalValueOfPOCurrency { get; set; }
        public string? Margin { get; set; }
        public string? RepeatOrder { get; set; }
        public string? ReboundPurchaserDivision { get; set; }
        public string? GTClinetForPO { get; set; }
        public string? Warehouse { get; set; }
        public string? CustomerDefinedVendor { get; set; }
        public bool? IsSendToQuality { get; set; }
        public int SupplierApprovalStatus { get; set; }
        public bool? ApproverPartERAIReported { get; set; }
        public string? SupplierType { get; set; }
        public bool? IsPOApproved { get; set; }
        public bool InDraftMode { get; set; }
        public bool IsLineManagerApprovalPermission { get; set; }
        public bool IsQualityTeamApprovalPermission { get; set; }
        public bool ISEscalationApprovalPermission { get; set; }
        public string? LineManagerComment { get; set; }
        public string? QualityComment { get; set; }
        public int CountryOnHighRisk { get; set; }
        public int? ClientNo { get; set; }
        public string? WarrantyPeriod { get; set; }
        public string? CommentText { get; set; }  // Comment Text
        public string? ApprovedDated { get; set; }
        public string? PartERAIReported { get; set; }
        public string? TradeReferenceOne { get; set; }
        public string? TradeReferenceTwo { get; set; }
        public string? TradeRefrenceThree { get; set; }
        public bool IsQualityUser { get; set; }
        public string? SupplierAdvisoryNotes { get; set; }
    }
}
