﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Core.Domain.Entities
{
    public class SupplierPoApprovalReadModel
    {
        public int SupplierApprovalId { get; set; }
        public int SupplierId { get; set; }
        public string? SupplierName { get; set; }
        public int ApprovedOrdersCount { get; set; }
        public int SupplierRMAsCount { get; set; }
        public string? PurchasingMethod { get; set; }
        public string? QualityApproval { get; set; }
        public string? QualityApprovedBy { get; set; }
        public string? LineManagerApproval { get; set; }
        public string? LineManagerApprovedBy { get; set; }
        public DateTime? QualityDLUP { get; set; }
        public DateTime? LineManagerDLUP { get; set; }
        public string? PartNo { get; set; }
        public int? Quantity { get; set; }
        public string? PaymentTerms { get; set; }
        public string? Incoterms { get; set; }
        public string? ShipVia { get; set; }
        public string? ShipFromCountry { get; set; }
        public string? TotalValueOfPOCurrency { get; set; }
        public string? Margin { get; set; }
        public string? RepeatOrder { get; set; }
        public string? ReboundPurchaserDivision { get; set; }
        public string? GTClinetForPO { get; set; }
        public string? Warehouse { get; set; }
        public string? CustomerDefinedVendor { get; set; }
        public int SupplierApprovalStatus { get; set; }
        public string? CompanyType { get; set; }
        public string? TradeRefComment { get; set; }  // Comment Text
        public string? LineManagerComment { get; set; }
        public string? QualityComment { get; set; }
        public int WarrantyPeriod { get; set; }
        public int? CountryOnHighRisk { get; set; }
        public bool? POApproved { get; set; }
        public bool? ApproverPartERAIReported { get; set; }
        public bool? IsSendToQuality { get; set; }
        public int IsEscalate { get; set; }
        public bool SupplierERAIReported { get; set; }
        public bool PartIsERAIReported { get; set; }
        public bool? IsQualityApproved { get; set; }
        public bool? IsLineManagerApproved { get; set; }
        public bool InDraftMode { get; set; }
        public int IsLineManagerApprovalPermission { get; set; }
        public bool IsQualityTeamApprovalPermission { get; set; }
        public bool ISEscalationApprovalPermission { get; set; }
    }
}
