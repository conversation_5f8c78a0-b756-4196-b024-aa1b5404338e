﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Orders.UserCases.Orders.Quotes.Queries.GetListQuoteForTab
{
    public class GetListQuoteForTabQuery : IRequest<BaseResponse<List<QuoteReadModel>>>
    {
        public int ClientId { get; set; }
        public int? TeamId { get; set; }
        public int? DivisionId { get; set; }
        public int? LoginId { get; set; }
        public int OrderBy { get; set; } = 1;
        public int SortDir { get; set; } = 1;
        public int PageIndex { get; set; } = 0;
        public int PageSize { get; set; } = 10;
        public string PartSearch { get; set; }
        public string ContactSearch { get; set; }
        public string CMSearch { get; set; }
        public int? SalesmanSearch { get; set; }
        public bool IncludeClosed { get; set; } = false;
        public int? QuoteNoLo { get; set; }
        public int? QuoteNoHi { get; set; }
        public DateTime? DateQuotedFrom { get; set; }
        public DateTime? DateQuotedTo { get; set; }
        public bool RecentOnly { get; set; } = true;
        public bool? Important { get; set; }
        public double? TotalLo { get; set; }
        public double? TotalHi { get; set; }
        public int? QuoteStatus { get; set; }
        public double? TotalProfitLo { get; set; }
        public double? TotalProfitHi { get; set; }
        public bool? AS6081 { get; set; }
        public int? SelectedClientNo { get; set; }
        public int? SelectedLoginID { get; set; }
    }
}
